<?php
namespace app\admin\model;

use think\Model;

class ApplyFormItem extends Model
{
    // 表名
    protected $name = 'eb_apply_form_item';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    
    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = 'delete_time';
    
    // 追加属性
    protected $append = [
        'status_text'
    ];
    
    public function getStatusList()
    {
        return [
            0 => __('未支付'),
            1 => __('已支付')
        ];
    }
    
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    
    // 关联用户
    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id');
    }
    
    // 关联订单
    public function orders()
    {
        return $this->hasMany('Order', 'apply_id', 'id');
    }
}
