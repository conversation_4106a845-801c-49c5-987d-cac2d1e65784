<?php
namespace app\admin\controller;

use app\common\model\TeacherPool;

class TeacherPool extends \app\common\controller\Backend
{
    protected $noNeedLogin = [];
    protected $noNeedRight = [];
    
    public function index()
    {
        if ($this->request->isAjax()) {
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = TeacherPool::where($where)
                ->order($sort, $order)
                ->paginate($limit);
            return json(['total' => $list->total(), 'rows' => $list->items()]);
        }
        return $this->view->fetch();
    }
    
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();
            $teacher = new TeacherPool();
            $teacher->save($params);
            $this->success();
        }
        return $this->view->fetch();
    }
    
    public function edit($ids = null)
    {
        $teacher = TeacherPool::get($ids);
        if ($this->request->isPost()) {
            $params = $this->request->post();
            $teacher->save($params);
            $this->success();
        }
        $this->assign('row', $teacher);
        return $this->view->fetch();
    }
    
    public function del($ids = null)
    {
        if ($ids) {
            TeacherPool::destroy($ids);
            $this->success();
        }
        $this->error();
    }
    
    public function audit($ids = null, $status = 1)
    {
        if ($ids) {
            TeacherPool::audit($ids, $status);
            $this->success();
        }
        $this->error();
    }
    
    public function import()
    {
        if ($this->request->isPost()) {
            $file = $this->request->file('file');
            // 处理导入逻辑
            $this->success();
        }
        return $this->view->fetch();
    }
    
    public function export()
    {
        $list = TeacherPool::export();
        // 导出Excel逻辑
        $this->success();
    }
}