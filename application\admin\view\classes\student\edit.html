<form id="edit-form" class="form-horizontal" style="position:relative; background: #fff" role="form"
      data-toggle="validator" method="POST" action="">
    <div class="panel-body">
        <ul class="nav nav-tabs" role="tablist">
            <li class="active"><a href="#tabs_0" role="tab" data-toggle="tab">基本信息</a></li>
            <li><a href="#tabs_1" role="tab" data-toggle="tab">详细信息</a></li>
            <li><a href="#tabs_2" role="tab" data-toggle="tab">状态</a></li>
        </ul>

        <div class="tab-content">
            <div class="tab-pane fade active in" id="tabs_0" style="padding: 15px;">
                <input type="hidden" id="row" name="data" value='<?php echo json_encode($row);?>' />
                <input type="hidden" id="id" name="row[id]" value='{$row.id}'/>
                <div class="form-group">
                    <label for="c-classes_id" class="control-label col-xs-12 col-sm-2">班级:</label>
                    <div class="col-xs-12 col-sm-8" id="div-classes_id">
                        <select  id="c-classes_id" name="row[classes_id]" class="form-control"
                                title="classes_id" data-rule="integer" placeholder="只能填入整数" data-tip="班级"></select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="c-student_id" class="control-label col-xs-12 col-sm-2">学生:</label>
                    <div class="col-xs-12 col-sm-8" id="div-student_id">
                        <select  id="c-student_id" name="row[student_id]" value="{$row.student_id}"
                                class="form-control" title="student_id" placeholder="'0'" data-rule="" data-tip="学生"></select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="c-add_time" class="control-label col-xs-12 col-sm-2">加入时间:</label>
                    <div class="col-xs-12 col-sm-8" id="div-add_time">
                        <input type="text" id="c-add_time" name="row[add_time]" value="{$row.add_time}"
                               class="form-control datetimepicker" data-use-current="true"
                               data-date-format="YYYY-MM-DD HH:mm:ss" data-tip="加入时间" data-rule=""/>
                    </div>
                </div>
            </div>

            <div class="tab-pane fade" id="tabs_1" style="padding: 15px;">
                <div class="form-group">
                    <label for="c-apply_status" class="control-label col-xs-12 col-sm-2">报名状态:</label>
                    <div class="col-xs-12 col-sm-8" id="div-apply_status">
                        <input id="c-apply_status" name="row[apply_status]" type="hidden"
                               value="{$row.apply_status|default='0'}">
                        <a href="javascript:;" data-toggle="switcher" class="btn-switcher"
                           data-input-id="c-apply_status" data-yes="1" data-no="0" data-size="small">
                            <i class="fa fa-toggle-on text-success {if $row.apply_status == '0'}fa-flip-horizontal text-gray{/if} fa-2x"></i>
                        </a>

                    </div>
                </div>

                <div class="form-group">
                    <label for="c-apply_id" class="control-label col-xs-12 col-sm-2">支付定单号:</label>
                    <div class="col-xs-12 col-sm-8" id="div-apply_id">
                        <input type="number" id="c-apply_id" name="row[apply_id]" value="{$row.apply_id}"
                               class="form-control" title="apply_id" data-rule="integer" placeholder="只能填入整数"
                               data-tip="支付定单号"/>
                    </div>
                </div>
                <div class="form-group">
                    <label for="c-school_id" class="control-label col-xs-12 col-sm-2">学校:</label>
                    <div class="col-xs-12 col-sm-8" id="div-school_id">
                        <input type="number" id="c-school_id" name="row[school_id]" value="0" class="form-control"
                               title="school_id" data-rule="integer" placeholder="只能填入整数" data-tip="学校"/>
                    </div>
                </div>
            </div>

            <div class="tab-pane fade" id="tabs_2" style="padding: 15px;">
                <div class="form-group">
                    <label for="c-status" class="control-label col-xs-12 col-sm-2">状态:</label>
                    <div class="col-xs-12 col-sm-8" id="div-status">
                        <input id="c-status" name="row[status]" type="hidden"
                               value="<?php echo htmlspecialchars($row['status']); ?>">
                        <a href="javascript:;" data-toggle="switcher"
                           class="btn-switcher <?php if ($row['status'] == 1) echo 'active'; else echo 'text-gray'; ?>"
                           data-input-id="c-status" data-yes="1" data-no="0">
                            <i class="fa fa-toggle-on text-success <?php if ($row['status'] != 1) echo 'fa-flip-horizontal'; ?> fa-2x"></i>
                        </a>
                        <div data-favisible="switch=1" class="p-3">已开户</div>
                        <div data-favisible="switch=0" class="p-3">关闭</div>
                    </div>
                </div>
            </div>

            <div class="form-group layer-footer">
                <label class="control-label col-xs-12 col-sm-2"></label>
                <div class="col-xs-12 col-sm-8">
                    <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
                    <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                </div>
            </div>
        </div>
    </div>
</form>