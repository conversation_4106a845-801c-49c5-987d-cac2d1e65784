<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    
    <div class="panel-body">
        <div id="myTabContent" class="tab-content" >
            <div class="tab-pane fade active in" id="tabs_0">
                <input type="hidden" name="row[id]" value="" class="form-control" data-rule="" data-tip="id"  />

                   <div class="form-group">
                        <label for="c-title" class="control-label col-xs-12 col-sm-2">标题:</label>
                        <div class="col-xs-12 col-sm-8" id="div-title">
                            <input type="text" id="c-title" name="row[title]" value="" class="form-control" title="title"  placeholder="" data-rule=""  data-tip="标题"  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-column_id" class="control-label col-xs-12 col-sm-2">栏目:</label>
                        <div class="col-xs-12 col-sm-8" id="div-column_id">
                            <input id="c-column_id" data-rule="require" data-select-only="true"
                                   data-source="/adminht.php/column/column/lists?type=1" data-multiple="false" data-field="name" data-value=""
                                   class="form-control selectpage" name="row[column_id]"
                                   type="text" value="" data-tip="栏目">
                        </div>
                   </div>

                    <!-- 抓取网址功能 -->
                    <div class="form-group">
                        <label for="c-s_url" class="control-label col-xs-12 col-sm-2">抓取网址:</label>
                        <div class="col-xs-12 col-sm-8" id="div-s_url">
                            <div class="form-inline">
                                <input type="text" id="c-s_url" name="s_url" value="" class="form-control" style="width: 70%;" placeholder="请输入要抓取的网址" />
                                <button type="button" id="btn-collect" class="btn btn-primary" style="margin-left: 10px;">
                                    <i class="fa fa-download"></i> 抓取
                                </button>
                            </div>
                        </div>
                    </div>

                <div class="form-group">
                    <label for="c-thumb" class="control-label col-xs-12 col-sm-2">封面图:</label>
                    <div class="col-xs-12 col-sm-8" id="div-thumb">
                        <div class="form-inline">
                            <input id="c-thumb" class="form-control" size="38" name="row[thumb]" type="text" value="" data-tip="">
                            <span>
                                <button type="button" id="plupload-thumb" class="btn btn-danger plupload" data-input-id="c-thumb" data-mimetype="image/*" data-multiple="false" data-preview-id="p-thumb">
                                    <i class="fa fa-upload"></i> {:__('Upload')}
                                </button>
                            </span>
                            <span>
                                <button type="button" id="fachoose-thumb" class="btn btn-primary fachoose" data-input-id="c-thumb" data-mimetype="image/*" data-multiple="false">
                                    <i class="fa fa-list"></i> {:__('Choose')}
                                </button>
                            </span>
                            <ul class="row list-inline plupload-preview" id="p-thumb"></ul>
                        </div>
                    </div>
                </div>

               <div class="form-group">
                    <label for="c-content" class="control-label col-xs-12 col-sm-2">内容:</label>
                    <div class="col-xs-12 col-sm-8" id="div-content">
                        <textarea name="row[content]" id="editor-content" class="form-control editor" data-rule="" rows="5" data-tip="内容" ></textarea>
                    </div>
               </div>

                <!-- 视频上传控件 -->
                <div class="form-group">
                    <label for="c-video" class="control-label col-xs-12 col-sm-2">视频:</label>
                    <div class="col-xs-12 col-sm-8" id="div-video">
                        <div class="form-inline">
                            <input id="c-video" class="form-control" size="38" name="row[video]" type="text"
                                   value="" data-tip="">
                            <span><button type="button" id="plupload-video" class="btn btn-danger plupload"
                                      data-input-id="c-video" data-mimetype="video/*" data-multiple="false"
                                      data-preview-id="p-video"><i
                                class="fa fa-upload"></i> 上传</button></span>
                            <span><button type="button" id="fachoose-video" class="btn btn-primary fachoose"
                                      data-input-id="c-video" data-mimetype="video/*" data-multiple="false"><i
                                class="fa fa-list"></i> 选择</button></span>
                            <ul class="row list-inline plupload-preview" id="p-video"></ul>
                        </div>
                    </div>
                </div>





                <div class="form-group layer-footer">
                    <label class="control-label col-xs-12 col-sm-2"></label>
                    <div class="col-xs-12 col-sm-8">
                        <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
                        <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                    </div>
                </div>
            </div>
            
        </div>
    </div>
</form>



