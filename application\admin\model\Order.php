<?php

namespace app\admin\model;

use think\Model;

class Order extends Model
{
// 表名
    public $name = 'fa_order';
    public $prefix_new = 'fa_';
// 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

// 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = 'delete_time';

// 字段映射
    public function getAddresslv_idTextAttr($value)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("School")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    public function getTable_idTextAttr($value)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("Subjectlv")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    public function getClasses_idTextAttr($value)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("Classes")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    public function getUser_idTextAttr($value)
    {
        return $value ? '学员ID: ' . $value : '';
    }

    public function getPayment_statusTextAttr($value)
    {
        $status = [0 => '未支付', 1 => '已支付'];
        return isset($status[$value]) ? $status[$value] : '';
    }
    public function getSchool_idTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

}