<?php

namespace app\admin\model;

use think\Model;
use fast\Tree;
use traits\model\SoftDelete;

class TeacherCommunity extends Model
{
    use SoftDelete;

    // 表名
    public $name = 'teacher';
    public $prefix_new = 'fa_';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = 'delete_time';
    // 追加属性
    protected $append = [
        'gender_text',
        'education_text',
        'title_text',
        'status_text'
    ];
    
    // 性别列表
    public function getGenderList()
    {
        return ['1' => __('男'), '2' => __('女')];
    }
    
    // 学历列表
    public function getEducationList()
    {
        return [
            '1' => __('专科'),
            '2' => __('本科'),
            '3' => __('硕士'),
            '4' => __('博士'),
            '5' => __('其他')
        ];
    }
    
    // 职称列表
    public function getTitleList()
    {
        return [
            '1' => __('助教'),
            '2' => __('讲师'),
            '3' => __('副教授'),
            '4' => __('教授'),
            '5' => __('其他')
        ];
    }
    
    // 状态列表
    public function getStatusList()
    {
        return ['0' => __('禁用'), '1' => __('正常')];
    }
    
    // 性别获取器
    public function getGenderTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['gender']) ? $data['gender'] : '');
        $list = $this->getGenderList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    
    // 学历获取器
    public function getEducationTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['education']) ? $data['education'] : '');
        $list = $this->getEducationList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    
    // 职称获取器
    public function getTitleTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['title']) ? $data['title'] : '');
        $list = $this->getTitleList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    
    // 状态获取器
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    
    // 社区关联
    public function community()
    {
        return $this->belongsTo('Community', 'community_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    
    // 课程关联
    public function courses()
    {
        return $this->belongsToMany('Course', 'teacher_course', 'course_id', 'teacher_id');
    }
    
    // 科目关联
    public function getSubjectIdsTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['subject_ids']) ? $data['subject_ids'] : '');
        $valueArr = explode(',', $value);
        $list = \app\admin\model\Subject::where('id', 'in', $valueArr)->column('name');
        return implode(',', $list);
    }
}