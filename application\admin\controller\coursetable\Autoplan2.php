<?php

namespace app\admin\controller\coursetable;

use app\admin\controller\subjectlv\Subjectlv;
use app\common\controller\Backend;
use fast\Tree;

/**
 *
 *
 * @icon fa fa-file
 */
class Autoplan extends Backend
{

    /**
     * Autoplan模型对象
     */
    protected $model = null;
    protected $noNeedRight = ["lists", "status", "export"];
    protected $noNeedLogin = ["lists", "status", "export"];


    public function _initialize()
    {
        parent::_initialize();
        $this->shopid = false;
        $this->model = model('Autoplan');
        $this->modelValidate = true;
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                ->where($where);
            $total = $total->where("status", "<>", -1);
            //if ($_SESSION['think']['admin']['group_id'] == 2) {
            //    $total = $total->where("city_id", "=", $_SESSION['think']['admin']['city_id']);
            //}
            $total = $total
                ->count();

            $list = $this->model
                ->where($where);
            //if ($_SESSION['think']['admin']['group_id'] == 2) {
            //    $list = $list->where("city_id", "=", $_SESSION['think']['admin']['city_id']);
            //}
            $list = $list->where("status", "<>", -1)
                ->order('weigh desc,id desc')
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();
            //$this->model->getLastSql();
            $list = collection($list)->toArray();

//            $tree = Tree::instance();
//            $tree->init($list, 'pid');
//            $list = $tree->getTreeList($tree->getTreeArray(0), 'name');
//            $data = array();
//            for ($i = 0; $i < count($list); $i++) {
//                $v = $list[$i];
//                $data[] = [
//                    'id' => $v['id'],
//                    'parent' => $v['pid'] ? $v['pid'] : '#',
//                    'name' => $v['name'],
//                    'type' => $v['type'],
//                    'status' => $v['status'],
//                    'school_id' => model("School")->getSchool_idTextAttr($v['school_id'])
//                ];
//            }


            for ($i = 0; $i < count($list); $i++) {
//                $list[$i]['province_id']=model('Cityarea')->getProvince_idTextAttr($list[$i]['province_id']);
//                $list[$i]['city_id']=model('Cityarea')->getCity_idTextAttr($list[$i]['city_id']);
//                $list[$i]['district_id']=model('Cityarea')->getDistrict_idTextAttr($list[$i]['district_id']);
//                $list[$i]['province_id']=preg_replace("/\/$/","",model('Cityarea')->getProvince_idTextAttr($list[$i]['province_id'])."/".$list[$i]['city_id']."/".$list[$i]['district_id']);
//                $list[$i]['school_id']=model('School')->getSchool_idTextAttr($list[$i]['school_id'],$list[$i]);
                //$v['classes_id']=$this->model->getClasses_idTextAttr($v['classes_id'],$v);
                $list[$i]['classes_id'] = $this->model->getClasses_idTextAttr($list[$i]['classes_id'], $list[$i]);
                //$v['ocation_id']=$this->model->getOcation_idTextAttr($v['ocation_id'],$v);
                $list[$i]['ocation_id'] = $this->model->getOcation_idTextAttr($list[$i]['ocation_id'], $list[$i]);
            }
            $result = array("total" => $total, "rows" => $list);
            if ($total > 0) {
                $result['rows'] = $list;
            } else {


                //在这里写预处理逻辑
                $r = model("Coursetable")->where("status", "=", 1)->where("id", "=", $_REQUEST['table_id'])->select();
                $coursetable_name = $r[0]["name"];//课程项目名称
                $classeseids = explode(",", $r[0]["classes_id"]);
                $r = model("Ocation")->where("status", "=", 1)->where("address_lv_octionlevel", "=", 3);
                if ($_SESSION['think']['admin']['rel_type'] == 1) {
                    $r = $r->where("rel_id", "=", $_SESSION['think']['admin']['school_id']);
                };
                $r = $r->select();
                $data = [];
                for ($i = 0; $i < count($r); $i++) {
                    $row = $r[$i];
                    $data[$row["name"]] = array();
                    $data[$row["name"]]["classroom_id"] = $row["id"];
                    $data[$row["name"]]["type"] = model("Ocation")->getOcation_typeTextAttr($row["ocation_type"]);
                    $data[$row["name"]]["daytimeoindex"] = !empty($row["schedule_data"]) ? json_decode($row["schedule_data"]) : [];
                    $data[$row["name"]]["max_lessons_per_week"] = $row["classes_per_week"];
                    $data[$row["name"]]["availability"] = weekdaysdaytime($row["weekdays"], $row["daytime"]);
                    $data[$row["name"]]["disabled_times"] = week_daytime($row["disable_week_range"]);
                    $data[$row["name"]]["disabled_slots"] = week_oindex($row["disable_week_time"]);
                    $data[$row["name"]]["time_slots"] = time_slots($row["schedule_data"]);
                    $data[$row["name"]]["start"] = substr($row["starttime"], 0, 10);
                    $data[$row["name"]]["end"] = substr($row["endtime"], 0, 10);
                }
                $classroomAvailability = $data;
                $r = model("Coursetable")->where("status", "=", 1)->where("id", "=", $_REQUEST['table_id'])->select();
                $startDate = substr($r[0]['start_date'], 0, 10);
                $endDate = "2028-01-01";

                $r = model("Classplant")->where("status", "=", 1)->where("table_id", "=", $_REQUEST['table_id'])->where("classes_id", "in", $classeseids)->select();
                $data = [];
                for ($i = 0; $i < count($r); $i++) {
                    $row = $r[$i];
                    $classesname = model("Classes")->getClasses_idTextAttr($row["classes_id"]);
                    $data[$classesname] = array();
                    $data[$classesname]["class_id"] = $row["id"];
                    $data[$classesname]["classes_id"] = $row["classes_id"];
                    $data[$classesname]["type"] = model("Subjectlv")->getSubject_lvTextAttr($row["subject_lv"]);
                    $data[$classesname]["type"] = $data[$classesname]["type"] == "" ? "语言" : $data[$classesname]["type"];
                    $data[$classesname]["subject_lv"] = $row["subject_lv"];
                    $data[$classesname]["count"] = $row["ocourse_count"];
                    $data[$classesname]["count_total"] = $row["ocourse_count"];
                    $data[$classesname]["classroom_type"] = explode(",", $row["ocation_type"]);
                    $data[$classesname]["classroom_type"] = empty($data[$classesname]["classroom_type"]) || $data[$classesname]["classroom_type"][0] == "" ? ["1", "2", "3", "4"] : $data[$classesname]["classroom_type"];
                    $data[$classesname]["classroom_type"] = array_map(function ($v) {
                        return model("Ocation")->getOcation_typeTextAttr($v);
                    }, $data[$classesname]["classroom_type"]);
                    $data[$classesname]["allow_multiple_classes_per_day"] = $row["can_same_day"] ? true : false;
                    $data[$classesname]["fixed_classroom"] = $row["same_classroom"] ? true : false;
                    $data[$classesname]["disabled_times"] = week_daytime($row["disable_week_range"]);
                    $data[$classesname]["allowed_days"] = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                    $data[$classesname]["required_times"] = week_daytime($row["must_week_range"]);
                    $data[$classesname]["max_classes_per_week"] = $row["classes_per_week"];
                    $data[$classesname]["teacher"] = model("User")->getUser_idTextAttr($row["teacher_id"]);
                    $data[$classesname]["nearby_classrooms"] = [];//请设为空，因为不知道安排哪个教室
                }
                $courses = $data;

//从下开始排课================================================================================
                $schedules = [];
                $publicHolidays = [
                    '2025-01-01', // 元旦
                    '2025-02-10', // 春节
                    '2025-02-11', // 春节
                    '2025-02-12', // 春节
                    '2025-04-04', // 清明节
                    '2025-05-01', // 劳动节
                    '2025-06-18', // 端午节
                    '2025-09-17', // 中秋节
                    '2025-10-01', // 国庆节
                    '2025-10-02', // 国庆节
                    '2025-10-03', // 国庆节
                ];


// 初始化排课表、教室占用情况和每周教室使用课节数
                $schedule = [];
                $classroomOccupancy = []; // 用于记录教室的时间占用情况
                $weeklyClassroomUsage = []; // 用于记录每周教室的已使用课节数
                $dailyClassUsage = []; // 用于记录每天每个班级的课程数
                $classroomAssignment = []; // 用于记录每个班级的固定教室分配
                $requiredTimesFailed = []; // 用于记录必排时间失败的情况
                $weeklyClassUsage = [];  // 用于记录每周每个班级的已使用课节数
                $teacherOccupancy = []; // 新增：用于记录老师的占用情况
                $nearbyClassroomUsage = []; // 新增：用于记录临近教室的使用情况

// 定义英文星期到中文星期的映射
                $weekdayMapping = [
                    'Monday' => '周一',
                    'Tuesday' => '周二',
                    'Wednesday' => '周三',
                    'Thursday' => '周四',
                    'Friday' => '周五',
                    'Saturday' => '周六',
                    'Sunday' => '周日'
                ];

// 定义中文星期到英文星期的映射
                $weekdayMappingReverse = array_flip($weekdayMapping);

// 初始化每个教室每天的使用情况
                $classroomDailyUsage = [];

// 初始化必排时段的安排状态
                $requiredTimesScheduled = [];


// 循环每一天
                $period = [];
                $currentDate = strtotime($startDate);
                while ($currentDate <= strtotime($endDate)) {
                    $period[] = date('Y-m-d', $currentDate);
                    $currentDate += 86400;
                }

                foreach ($period as $date) {
                    $date = new \DateTime($date);
                    $dayOfWeek = $weekdayMapping[$date->format('l')];
                    $dateString = $date->format('Y-m-d');
                    $weekKey = $date->format('Y-W'); // 用于标识当前周（年份 + 周数）

                    // 初始化每周教室使用课节数
                    if (!isset($weeklyClassroomUsage[$weekKey])) {
                        $weeklyClassroomUsage[$weekKey] = [];
                        foreach ($classroomAvailability as $classroom => $info) {
                            $weeklyClassroomUsage[$weekKey][$classroom] = 0;
                        }
                    }

                    // 初始化每周班级使用课节数
                    if (!isset($weeklyClassUsage[$weekKey])) {
                        $weeklyClassUsage[$weekKey] = [];
                        foreach ($courses as $class => $info) {
                            $weeklyClassUsage[$weekKey][$class] = 0;
                        }
                    }

                    // 跳过星期天和国定假日
                    if (in_array($dateString, $publicHolidays)) {
                        continue;
                    }

                    // 遍历每个教室
                    foreach ($classroomAvailability as $classroom => $info) {
                        $availability = $info['availability'];
                        $classroomType = $info['type']; // 教室类型
                        $maxLessonsPerWeek = $info['max_lessons_per_week']; // 每周最大课节数
                        $disabledTimes = $info['disabled_times'] ?? []; // 禁用时段
                        $disabledSlots = $info['disabled_slots'] ?? []; // 新增：禁用课节
                        $timeSlots = $info['time_slots']; // 时段划分
                        $classroomStart = new \DateTime($info['start']);
                        $classroomEnd = new \DateTime($info['end']);
                        $daytimeoindex = $info["daytimeoindex"];

                        // 检查当前日期是否在教室的使用期限内
                        if ($date >= $classroomStart && $date <= $classroomEnd) {
                            if (isset($availability[$dayOfWeek])) {
                                foreach ($availability[$dayOfWeek] as $timePeriod) {
                                    // 检查当前时段是否是禁用时段  $dayOfWeek周一$timePeriod上午
                                    if (isset($disabledTimes[$dayOfWeek]) && in_array($timePeriod, $disabledTimes[$dayOfWeek])) {
                                        continue; // 当前时段是禁用时段，跳过
                                    }

                                    // 遍历当前时段的课节
                                    if (array_key_exists($timePeriod, $timeSlots) && is_array($timeSlots[$timePeriod])) {

                                        foreach ($timeSlots[$timePeriod] as $timeSlot) {
                                            // 检查当前课节是否是禁用课节 $dayOfWeek周一$timePeriod上午  $timeSlot课节1
                                            if (
                                                (isset($disabledSlots[$dayOfWeek]) && in_array($timeSlot, $disabledSlots[$dayOfWeek])) ||
                                                (isset($disabledSlots['*']) && in_array($timeSlot, $disabledSlots['*']))
                                            ) {
                                                continue; // 当前课节是禁用课节，跳过
                                            }

                                            // 标记是否还有课程可排
                                            $hasCourseToSchedule = true;
                                            while ($hasCourseToSchedule) {
                                                // 重置标记
                                                $hasCourseToSchedule = false;

                                                // 检查教室在该课节是否已被占用
                                                $key = $classroom . '-' . $dateString . '-' . $timeSlot;
                                                if (isset($classroomOccupancy[$key])) {
                                                    break; // 课节已被占用，跳出循环
                                                }

                                                // 检查本周教室的课节数是否已达到上限
                                                if ($weeklyClassroomUsage[$weekKey][$classroom] >= $maxLessonsPerWeek) {
                                                    break; // 本周课节数已满，跳出循环
                                                }

                                                // 遍历每个班级
                                                foreach ($courses as $class => &$course) {

                                                    if ($dateString == "2025-01-31") {
                                                        if ($classroom == "502教室") {
                                                            if ($class == "2024级秋季声乐中级班") {
                                                                $bug = $course["type"] . $timePeriod . $timeSlot;
                                                            }
                                                        }
                                                    }


                                                    // 检查教室类型是否匹配
                                                    if ($course['count'] > 0 && in_array($classroomType, $course['classroom_type'])) {
                                                        // 检查是否允许同一天上两节课
                                                        if (!$course['allow_multiple_classes_per_day'] && isset($dailyClassUsage[$dateString][$class]) && $dailyClassUsage[$dateString][$class] >= 1) {
                                                            continue; // 不允许同一天上两节课，跳过
                                                        }

                                                        // 检查是否需要固定教室
                                                        if ($course['fixed_classroom'] && !isset($classroomAssignment[$class])) {
                                                            $classroomAssignment[$class] = $classroom; // 分配固定教室
                                                        } elseif ($course['fixed_classroom'] && $classroomAssignment[$class] != $classroom) {
                                                            continue; // 固定教室不匹配，跳过
                                                        }

                                                        // 检查班级是否禁用了当前时间段
                                                        if (isset($course['disabled_times'][$dayOfWeek]) && in_array($timePeriod, $course['disabled_times'][$dayOfWeek])) {
                                                            continue; // 班级禁用了当前时间段，跳过
                                                        }

                                                        // 检查班级是否只在特定天排课
                                                        if (!empty($course['allowed_days']) && !in_array($dayOfWeek, $course['allowed_days'])) {
                                                            continue; // 班级不在当前天排课，跳过
                                                        }

                                                        // 检查班级本周已排课节数是否已达到上限
                                                        if (isset($course['max_classes_per_week']) && $weeklyClassUsage[$weekKey][$class] >= $course['max_classes_per_week']) {
                                                            continue; // 本周已排课节数已达到上限，跳过
                                                        }


                                                        // 检查老师是否已被占用
                                                        $teacherKey = $course['teacher'] . '-' . $dateString . '-' . $timeSlot;
                                                        if (isset($teacherOccupancy[$teacherKey])) {
                                                            continue; // 老师已被占用，跳过
                                                        }

                                                        // 检查临近教室是否已被占用
                                                        $nearbyKey = $timePeriod . '-' . $dateString;
                                                        if (empty($course['nearby_classrooms'])) {
                                                            $course['nearby_classrooms'] = getNearByClassroom($info['classroom_id']);
                                                        }
                                                        if (isset($nearbyClassroomUsage[$nearbyKey]) && !in_array($classroom, $course['nearby_classrooms'])) {
                                                            continue; // 当前教室不是临近教室，跳过
                                                        }

                                                        // 检查是否是必排时间段
                                                        if (isset($course['required_times'][$dayOfWeek]) && in_array($timePeriod, $course['required_times'][$dayOfWeek])) {
                                                            // 必排时间段，优先排课
                                                            $info_oindex_data = getdaytimeOindexinfo($info, $timePeriod, $timeSlot);
                                                            $schedule[] = [
                                                                '班名' => $class,
                                                                'class_id' => $course['class_id'],
                                                                '教室' => $classroom . "({$info['type']})",
                                                                'ocation_id' => $info["classroom_id"],
                                                                '教室类型' => $info["type"],
                                                                'subject_lv' => $course["subject_lv"],
                                                                '课程' => model("Subjectlv")->getSubject_lvTextAttr($course["subject_lv"]),
                                                                '日期' => $dateString,
                                                                'date' => $dateString,
                                                                '周几' => $dayOfWeek, // 添加周几字段
                                                                '时段' => $timePeriod,
                                                                '课节' => $timeSlot,
                                                                '备注' => '必排时间段',
                                                                '老师' => $course['teacher'], // 新增：老师
                                                                '课节合计' => count($info["daytimeoindex"]),
                                                                'start_time' => $info_oindex_data->start_time,
                                                                'end_time' => $info_oindex_data->end_time,
                                                                'classes_id' => $course['classes_id'],
                                                                'count_total' => $course['count_total'],
                                                                'class_index' => $course['count_total'] - $course['count'] + 1
                                                            ];
                                                            $course['count']--;
                                                            $classroomOccupancy[$key] = true; // 标记该课节已被占用
                                                            $weeklyClassroomUsage[$weekKey][$classroom]++; // 增加本周教室的已使用课节数
                                                            $weeklyClassUsage[$weekKey][$class]++; // 增加本周班级的已使用课节数
                                                            $teacherOccupancy[$teacherKey] = true; // 标记老师已被占用
                                                            $nearbyClassroomUsage[$nearbyKey] = $classroom; // 标记临近教室已被占用

                                                            // 更新每天每个班级的课程数
                                                            if (!isset($dailyClassUsage[$dateString][$class])) {
                                                                $dailyClassUsage[$dateString][$class] = 0;
                                                            }
                                                            $dailyClassUsage[$dateString][$class]++;

                                                            // 标记必排时段已安排
                                                            $requiredTimesScheduled[$class][$dateString] = true;

                                                            // 记录教室每天的使用情况
                                                            if (!isset($classroomDailyUsage[$classroom][$dateString])) {
                                                                $classroomDailyUsage[$classroom][$dateString] = [];
                                                            }
                                                            $classroomDailyUsage[$classroom][$dateString][$timePeriod . ' ' . $timeSlot] = $class;

                                                            $hasCourseToSchedule = true; // 标记还有课程可排
                                                            break;
                                                        } else {

                                                            // 非必排时间段，检查是否已安排必排时段
                                                            if (isset($course['required_times'][$dayOfWeek]) && !isset($requiredTimesScheduled[$class][$dateString])) {
                                                                continue; // 必排时段未安排，跳过非必排时段
                                                            }

                                                            $info_oindex_data = getdaytimeOindexinfo($info, $timePeriod, $timeSlot);
                                                            // 非必排时间段，正常排课
                                                            $schedule[] = [
                                                                '班名' => $class,
                                                                'class_id' => $course['class_id'],
                                                                '教室' => $classroom . "({$info['type']})",
                                                                'ocation_id' => $info["classroom_id"],
                                                                '教室类型' => $info["type"],
                                                                'subject_lv' => $course["subject_lv"],
                                                                '日期' => $dateString,
                                                                'date' => $dateString,
                                                                '周几' => $dayOfWeek, // 添加周几字段
                                                                '时段' => $timePeriod,
                                                                '课程' => model("Subjectlv")->getSubject_lvTextAttr($course["subject_lv"]),
                                                                '课节' => $timeSlot,
                                                                '课节合计' => count($info["daytimeoindex"]),
                                                                '备注' => '',
                                                                '老师' => $course['teacher'],
                                                                'classes_id' => $course['classes_id'],
                                                                'start_time' => $info_oindex_data->start_time,
                                                                'end_time' => $info_oindex_data->end_time,
                                                                'count_total' => $course['count_total'],
                                                                'class_index' => $course['count_total'] - $course['count'] + 1
                                                            ];
                                                            $course['count']--;
                                                            $classroomOccupancy[$key] = true; // 标记该课节已被占用
                                                            $weeklyClassroomUsage[$weekKey][$classroom]++; // 增加本周教室的已使用课节数

                                                            $weeklyClassUsage[$weekKey][$class]++; // 增加本周班级的已使用课节数
                                                            $teacherOccupancy[$teacherKey] = true; // 标记老师已被占用
                                                            $nearbyClassroomUsage[$nearbyKey] = $classroom; // 标记临近教室已被占用

                                                            // 更新每天每个班级的课程数
                                                            if (!isset($dailyClassUsage[$dateString][$class])) {
                                                                $dailyClassUsage[$dateString][$class] = 0;
                                                            }
                                                            $dailyClassUsage[$dateString][$class]++;

                                                            // 记录教室每天的使用情况
                                                            if (!isset($classroomDailyUsage[$classroom][$dateString])) {
                                                                $classroomDailyUsage[$classroom][$dateString] = [];
                                                            }
                                                            $classroomDailyUsage[$classroom][$dateString][$timePeriod . ' ' . $timeSlot] = $class;
                                                            $hasCourseToSchedule = true; // 标记还有课程可排
                                                            break;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                foreach ($courses as $class => $course) {
                    foreach ($course['required_times'] as $day => $times) {
                        foreach ($times as $time) {
                            $found = false;
                            foreach ($schedule as $entry) {
                                if ($entry['班名'] == $class && $entry['周几'] == $day && $entry['时段'] == $time) {
                                    $found = true;
                                    break;
                                }
                            }
                            if (!$found) {
                                // 获取必排时间段的具体日期
                                $requiredDate = new \DateTime($startDate);
                                $dayOfWeek = $weekdayMappingReverse[$day];
                                while ($requiredDate->format('l') !== $dayOfWeek) {
                                    $requiredDate->modify('next ' . $dayOfWeek);
                                }
                                if ($requiredDate < $startDate) {
                                    $requiredDate->modify('next ' . $dayOfWeek);
                                }
                                $requiredDateStr = $requiredDate->format('Y-m-d');
                                $requiredTimesFailed[] = [
                                    '班名' => $class,
                                    '日期' => $requiredDateStr,
                                    '周几' => $day,
                                    '时段' => $time,
                                    '备注' => '必排时间段未排上',
                                ];
                            }
                        }
                    }
                }

                $rows2 = array();
                foreach ($schedule as $entry) {
                    $array = array();
                    $array['table_id'] = $_REQUEST["table_id"];
                    $array['classes_id'] = $entry['classes_id'];
                    $array['ocation_id'] = $entry['ocation_id'];
                    $array['subject_lv'] = $entry['subject_lv'];
                    $array['date'] = $entry['date'];
                    $array['start_time'] = $entry['start_time'];
                    $array['end_time'] = $entry['end_time'];
                    $array['week'] = $entry['周几'];
                    $array['daytime'] = $entry['时段'];
                    $array['teacher'] = $entry['老师'];
                    $array['class_index'] = preg_replace("/课|节/", "", $entry['课节']);
                    $array['count_total'] = $entry['课节合计'];
                    $array['bz'] = $entry['备注'];
                    $rows2[] = $array;
                }
                model("Autoplantemp")->insertUpdate($rows2, ['table_id', 'classes_id', 'ocation_id', 'date', 'start_time', 'class_index']);
                $total = mode("Autoplantemp");
                $total = $total->where("table_id", "=", $_REQUEST["table_id"]);
                $total = $total->where("status", "<>", -1)
                    ->count();

                $list = mode("Autoplantemp");
                $list = $list->where("table_id", "=", $_REQUEST["table_id"]);
                $list = $list->where("status", "<>", -1)
                    ->order('id')
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();
                $list = collection($list)->toArray();
            }

            $message = "";
            foreach ($requiredTimesFailed as $entry) {
                $message .= "警告: " . $entry['班名'] . " 在 " . $entry['日期'] . " " . $entry['周几'] . " " . $entry['时段'] . " 未排上课。" . $entry['备注'] . "\n<br>";
            }

            // 检查是否有未排课的班级
            foreach ($courses as $class => $course) {
                if ($course['count'] > 0) {
                    $message .= "警告: {$class} 还有 {$course['count']} 节课未安排。\n<br>";
                }
            }

            // 输出每个教室每天所有时段的上课情况
            //echo "\n每个教室每天所有时段的上课情况：\n";
            foreach ($classroomDailyUsage as $classroom => $days) {
                $message .= "教室: $classroom\n<br>";
                foreach ($days as $date => $timeSlots) {
                    $dateObj = new \DateTime($date);
                    $dayOfWeek = $weekdayMapping[$dateObj->format('l')];
                    $message .= "  日期: $date  ($dayOfWeek)\n<br>";
                    foreach ($timeSlots as $timeSlot => $className) {
                        $message .= "    时段: $timeSlot - 班级: $className\n<br>";
                    }
                }
                $message .= "\n<br>";
            }
            $result["total"] = count($rows);
            $result["message"] = $message;
            $result["rows"] = $rows;
            return json($result);
        }

//        $this->assignconfig("relType",$_REQUEST['relType']);
//        $this->assignconfig("relId",$_REQUEST['relId']);
        $this->assignconfig("relType", $_REQUEST['relType']);
        $this->assignconfig("table_id", $_REQUEST['table_id']);
        $this->assignconfig("admin", $_SESSION['think']['admin']);
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validate($validate);
                    }
                    $datatime = date("Y-m-d H:i:s");
                    $params['create_time'] = $datatime;
                    unset($params['delete_time']);
                    unset($params['update_time']);
//                    if($_SESSION['think']['admin']['group_id']==2){
//                        $params['city_id']=$_SESSION['think']['admin']['city_id'];
//                    }
                    $params['date'] = (!array_key_exists("date", $params) || $params['date'] == "") ? date("Y-m-d", time()) : date("Y-m-d", strtotime($params['date']));
                    $result = $this->model->allowField(true)->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($this->model->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->assignconfig("admin", $_SESSION['think']['admin']);
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public
    function edit($ids = NULL)
    {
        $row = $this->model->get($ids);
        if (!$row)
            $this->error(__('No Results were found'));
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validate($validate);
                    }
                    $datatime = date("Y-m-d H:i:s");
                    $params['update_time'] = $datatime;
                    unset($params['delete_time']);
                    unset($params['create_time']);
//                    if($_SESSION['think']['admin']['group_id']==2){
//                        $params['city_id']=$_SESSION['think']['admin']['city_id'];
//                    }
                    $params['date'] = (!array_key_exists("date", $params) || $params['date'] == "") ? date("Y-m-d", time()) : date("Y-m-d", strtotime($params['date']));
                    $result = $row->allowField(true)->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($row->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->assignconfig("admin", $_SESSION['think']['admin']);
        $this->view->assign("row", $row);
        return $this->view->fetch();

    }

    /**
     * 添加
     */
    public
    function view($ids = NULL)
    {
        $row = $this->model->getRow($ids);
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    public
    function lists($ids = NULL)
    {
        $where = "where 1=1";
        if ($_SESSION['think']['admin']['group_id'] != 1) {
            $where .= " and city_id='{$_SESSION['think']['admin']['city_id']}'";
        }
        $page = 1;
        $pagesize = 50;
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                $keyField = $this->request->request("keyField");
                $keyValue = $this->request->request("keyValue");
                $page = $this->request->request("pageNumber");
                $page = ($page == null) ? 1 : $page;
                $pagesize = $this->request->request("pageSize");
                $pagesize = ($pagesize == null) ? 10 : $pagesize;
                if ($keyValue != "") {
                    $nn = preg_split("/\,/", $keyValue);
                    for ($i = 0; $i < count($nn); $i++) {
                        $where .= " and $keyField='{$nn[$i]}'";
                    }
                }
            } else {
                $where = "where 1=1";
                if (isset($_REQUEST['city_id']) && $_REQUEST['city_id'] > 0) {
                    //$where.=" and city_id=".$_REQUEST['city_id'];
                }
            }
        }
        $sql = "select id,week name from dev002_gxyusheng.fa_course_table_autoplan $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
        $list = db()->query($sql);

//        $r_name = model("School")->field("id,name")->where("id", "=", $_REQUEST['school_id'])->select();
//        if (empty($list)&&!empty($r_name)) {
//            $data = array(
//                "type" => "file",
//                "pid" => 0,
//                "name" => $r_name[0]['name']."学科",
//                "title" => $r_name[0]['name'],
//                "ismenu" => 0,
//                "createtime" => time(),
//                "updatetime" => time(),
//                "status" => 1,
//                "school_id" => $_REQUEST['school_id'],
//            );
//            $this->model->create($data);
//        }
//        $sql="select id,pid,name,title,status from dev002_gxyusheng.fa_course_table_autoplan $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
//        $list = db()->query($sql);
//        $tree = Tree::instance();
//        $tree->init($list, 'pid');
//        $list = $tree->getTreeList($tree->getTreeArray(0), 'name');

        $total = db()->query("select count(1) as c from  dev002_gxyusheng.fa_course_table_autoplan $where")[0];
        echo json_encode(array("list" => $list, "total" => $total['c']));
        exit;
    }

    public
    function status($ids = '')
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }
        try {
            $result = $this->model->where("id", "=", $ids)->field("status")->find();
            $v = $result->status == 1 ? 0 : 1;
            $this->model->where("id", "=", $ids)->update(["status" => $v]);
            $this->success('切换成功!');
        } catch (Exception $e) {
            $this->error('切换失败!' . $e->getMessage());
        }
    }

    public
    function changeStatus($ids = '')
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }
        try {
            $result = $this->model->where("id", "=", $ids)->field("status")->find();
            $v = $result->status == 1 ? 0 : 1;
            $this->model->where("id", "=", $ids)->update(["status" => $v]);
            $this->success('切换成功!');
        } catch (Exception $e) {
            $this->error('切换失败!' . $e->getMessage());
        }
    }

    public function export()
    {
        // 获取数据
        $list = $this->model->select();

        // 设置导出文件名
        $filename = '教学点下载' . date('YmdHis');

        // 设置表头
        $headers = [
            'Id',
            '项目名称',
            '班名',
            '教室',
            '课程',
            '老师',
            '日期',
            '周几',
            '时段',
            '课节',
            '备注',
            '操作',
        ];

// 设置数据
        $data = [];
        foreach ($list as $item) {
            $ocation = model("Ocation")->field("name")->get($item['id']);
            $courseable = model("Coursetable")->field("name")->get($item['table_id']);
            $classes = model("Classes")->field("name")->get($item['classes_id']);
            $data[] = [
                $item['id'],
                $item['project_name'],
                $classes['name'],
                $ocation['name'],
                $courseable['name'],
                $item['teacher'],
                $item['date'],
                $item['day_of_week'],
                $item['time_slot'],
                $item['class_period'],
                $item['remarks'],
                $item['operation'],
            ];
        }

        // 调用导出方法
        $this->exportExcel($filename, $headers, $data);
    }

    protected function exportExcel($filename, $headers, $data)
    {
        // 导出Excel文件
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        // 设置表头
        $sheet->fromArray($headers, null, 'A1');
        // 设置数据
        $sheet->fromArray($data, null, 'A2');

        // 设置列宽为内容自适应
        foreach (range('A', $sheet->getHighestColumn()) as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        // 设置响应头
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
        header('Cache-Control: max-age=0');
        // 输出文件
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

}
