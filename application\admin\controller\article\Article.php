<?php

namespace app\admin\controller\article;

use app\common\controller\Backend;
use fast\Tree;
/**
 * 
 *
 * @icon fa fa-file
 */
class Article extends Backend
{
    
    /**
     * Article模型对象
     */
    protected $model = null;
    protected $noNeedRight=['changestatus'];
    protected $noNeedLogin=['changestatus'];


    public function _initialize()
    {
        parent::_initialize();
        $this->shopid=false;
        $this->model = model('Article');
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                ->where($where)
                ->order($sort, $order)
                ->count();

            $list = $this->model
                ->where($where)
                //->order("weigh desc")
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();
            //$sql = $this->model->getLastSql();
            $list = collection($list)->toArray();
            for($i=0;$i<count($list);$i++){
                $list[$i]['city_id']=$this->model->getcityidTextAttr($list[$i]['city_id'],$list[$i]);
                $list[$i]['school_id']=$this->model->getschoolidTextAttr($list[$i]['school_id'],$list[$i]);
            }
            $result = array("total" => $total, "rows" => $list);
            return json($result);
        }
        return $this->view->fetch();
    }
    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                try {
                    // 验证栏目是否已选择
                    if (empty($params['column_id'])) {
                        $this->error('请先选择栏目');
                    }
                    
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validate($validate);
                    }
                    
                    $user = $this->auth->getUserInfo();
                    unset($params['id']);
                    $params['status'] = 1;
                    $params['userid'] = $user['id'];
                    $params['school_id'] = $user['school_id'];
                    $params['update_time']=(!array_key_exists("update_time",$params)||$params['update_time']=="")?date("Y-m-d H:i:s",time()):date("Y-m-d H:i:s",strtotime($params['update_time']));
                    $params['create_time']=(!array_key_exists("create_time",$params)||$params['create_time']=="")?date("Y-m-d H:i:s",time()):date("Y-m-d H:i:s",strtotime($params['create_time']));
                    $result = $this->model->allowField(true)->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($this->model->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = NULL)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post();

            // 处理视频上传 - with existence check
            if (isset($_FILES['video']) && $_FILES['video']['size'] > 0) {
                $video = $this->request->file('video');
                $videoConfig = [
                    'mimetype' => 'video/*',
                    'maxsize' => 1024 * 1024 * 100 // 100MB
                ];
                $videoInfo = Service\Upload::instance()->upload($video, 'video', 0, $videoConfig);
                if ($videoInfo) {
                    $params['video'] = $videoInfo->url;
                }
            }

            // 处理图片上传 - with existence check
            if (isset($_FILES['image']) && $_FILES['image']['size'] > 0) {
                $image = $this->request->file('image');
                $imageConfig = [
                    'mimetype' => 'image/*',
                    'maxsize' => 1024 * 1024 * 10 // 10MB
                ];
                $imageInfo = Service\Upload::instance()->upload($image, 'image', 0, $imageConfig);
                if ($imageInfo) {
                    $params['image'] = $imageInfo->url;
                }
            }

            if ($params) {
                try {
                    // 验证栏目是否已选择
                    if (empty($params['row']['column_id'])) {
                        $this->error('请先选择栏目');
                    }
                    
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $this->model->validate($validate);
                    }
                    $user = $this->auth->getUserInfo();
                    $params['row']['userid'] = $user['id'];
                    $params['row']['school_id'] = $user['school_id'];
                    $params['row']['update_time']=(!array_key_exists("update_time",$params)||$params['update_time']=="")?date("Y-m-d H:i:s",time()):date("Y-m-d H:i:s",strtotime($params['update_time']));
                    $params['row']['create_time']=(!array_key_exists("create_time",$params)||$params['create_time']=="")?date("Y-m-d H:i:s",time()):date("Y-m-d H:i:s",strtotime($params['create_time']));
                    $result = $row->allowField(true)->save($params['row']);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($row->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();

    }

    /**
     * 添加
     */
    public function view($ids = NULL)
    {
        $row=$this->model->getRow($ids);
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    public function lists($ids = NULL){
        $where="";
        $page=1;
        $pagesize=50;
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                $keyField = $this->request->request("keyField");
                $keyValue = $this->request->request("keyValue");
                $page = $this->request->request("pageNumber");
                $page = ($page == null) ? 1 : $page;
                $pagesize = $this->request->request("pageSize");
                $pagesize = ($pagesize == null) ? 10 : $pagesize;
                if ($keyValue != "") {
                    $nn=preg_split("/\,/",$keyValue);
                    $where="where 1=0";
                    for($i=0;$i<count($nn);$i++) {
                        $where.= " or $keyField='{$nn[$i]}'";
                    }
                }
            }
        }
        $sql="select id,title name from dev002_gxyusheng.fa_article $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
        $list = db()->query($sql);

        //$tree = Tree::instance();
        //$tree->init($list, 'pid');
        //if(isset($keyValue)&&$keyValue!=""){
        //}else{
        //    $list=$tree->getTreeList($tree->getTreeArray(0), 'name');
        //    //array_unshift($list,array("name"=>"无父级","pid"=>0,"weigh"=>9999));
        //}

        $total = db()->query("select count(1) as c from  dev002_gxyusheng.fa_article $where")[0];
        echo json_encode(array("list" => $list, "total" => $total['c']));
        exit;
    }

    /**
     * 更改状态
     */
    public function changeStatus($ids = '')
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }
        try {
            $result = $this->model->where("id", "=", $ids)->field("status")->find();
            $v = $result->status == 1 ? 0 : 1;
            $this->model->where("id", "=", $ids)->update(["status" => $v]);
            $this->success('切换成功!');
        } catch (Exception $e) {
            $this->error('切换失败!' . $e->getMessage());
        }
    }
}
