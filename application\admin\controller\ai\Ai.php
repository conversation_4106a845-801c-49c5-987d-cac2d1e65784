<?php

namespace app\admin\controller\ai;

use app\common\controller\Backend;
use think\Exception;

/**
 * AI应用控制器
 * 提供AI相关的通用功能
 */
class Ai extends Backend
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * AI自动生成图片
     */
    public function generateImage()
    {
        if (!$this->request->isPost()) {
            $this->error('请求方式错误');
        }
        
        $prompt = $this->request->post('prompt', '');
        $size = $this->request->post('size', '512x512');
        $style = $this->request->post('style', 'default');
        
        if (empty($prompt)) {
            $this->error('AI对话参数不能为空');
        }
        
        try {
            // 调用AI图片生成服务
            $imageUrl = $this->callAiApi($prompt, $size, $style);
            
            if ($imageUrl) {
                // 下载图片到本地
                $localPath = $this->downloadAndSaveImage($imageUrl);
                if ($localPath) {
                    $this->success('AI图片生成成功', null, [
                        'image_url' => $localPath,
                        'original_url' => $imageUrl,
                        'prompt' => $prompt,
                        'size' => $size,
                        'style' => $style
                    ]);
                } else {
                    $this->error('图片保存失败');
                }
            } else {
                $this->error('AI图片生成失败');
            }
        } catch (\Exception $e) {
            $this->error('生成失败：' . $e->getMessage());
        }
    }
    
    /**
     * 批量生成图片
     */
    public function batchGenerate()
    {
        if (!$this->request->isPost()) {
            $this->error('请求方式错误');
        }
        
        $prompts = $this->request->post('prompts', []);
        $size = $this->request->post('size', '512x512');
        $style = $this->request->post('style', 'default');
        
        if (empty($prompts) || !is_array($prompts)) {
            $this->error('提示词数组不能为空');
        }
        
        $results = [];
        $errors = [];
        
        foreach ($prompts as $index => $prompt) {
            try {
                $imageUrl = $this->callAiApi($prompt, $size, $style);
                if ($imageUrl) {
                    $localPath = $this->downloadAndSaveImage($imageUrl);
                    if ($localPath) {
                        $results[] = [
                            'index' => $index,
                            'prompt' => $prompt,
                            'image_url' => $localPath,
                            'original_url' => $imageUrl
                        ];
                    } else {
                        $errors[] = ['index' => $index, 'error' => '图片保存失败'];
                    }
                } else {
                    $errors[] = ['index' => $index, 'error' => 'AI图片生成失败'];
                }
            } catch (\Exception $e) {
                $errors[] = ['index' => $index, 'error' => $e->getMessage()];
            }
        }
        
        $this->success('批量生成完成', null, [
            'success_count' => count($results),
            'error_count' => count($errors),
            'results' => $results,
            'errors' => $errors
        ]);
    }
    
    /**
     * AI自动生成文本
     */
    public function generateText()
    {
        if (!$this->request->isPost()) {
            $this->error('请求方式错误');
        }
        
        $prompt = $this->request->post('prompt', '');
        $maxTokens = $this->request->post('max_tokens', 1000);
        
        if (empty($prompt)) {
            $this->error('提示词不能为空');
        }
        
        try {
            // 调用AI文本生成服务
            $text = $this->callTextGenerationApi($prompt, $maxTokens);
            
            if ($text) {
                $this->success('AI文本生成成功', null, [
                    'text' => $text,
                    'prompt' => $prompt,
                    'max_tokens' => $maxTokens
                ]);
            } else {
                $this->error('AI文本生成失败');
            }
        } catch (\Exception $e) {
            $this->error('生成失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取AI生成历史记录
     */
    public function getHistory()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);
        
        // 这里可以从数据库获取历史记录
        // 暂时返回空数据
        $this->success('获取成功', null, [
            'total' => 0,
            'rows' => []
        ]);
    }
    
    /**
     * 调用AI API生成图片
     */
    private function callAiApi($prompt, $size = '1024*1024', $style = 'default')
    {
        $config = config('ai.');
        $provider = $config['default_provider'] ?? 'qianwen';
        
        // 获取当前提供商的配置
        $providerConfig = $config[$provider] ?? $config['qianwen'];
        $apiKey = $providerConfig['api_key'];
        
        if (empty($apiKey)) {
            throw new Exception('AI API密钥未配置，请设置环境变量 AI_' . strtoupper($provider) . '_KEY');
        }
        
        $apiUrl = $providerConfig['api_url'];
        $model = $providerConfig['model'];
        
        // 根据风格调整提示词
        $enhancedPrompt = $this->enhancePrompt($prompt, $style);
        
        // 检查尺寸是否支持
        if (!in_array($size, $providerConfig['supported_sizes'])) {
            $size = $providerConfig['max_size'];
        }
        
        if ($provider === 'qianwen') {
            return $this->callQianwenImageApi($enhancedPrompt, $size, $apiKey, $apiUrl, $model, $config);
        } else {
            return $this->callOpenAIImageApi($enhancedPrompt, $size, $apiKey, $apiUrl, $model, $config);
        }
    }
    
    /**
     * 调用阿里千问图片生成API
     */
    private function callQianwenImageApi($prompt, $size, $apiKey, $apiUrl, $model, $config)
    {
        $data = [
            'model' => $model,
            'input' => [
                'prompt' => $prompt
            ],
            'parameters' => [
                'size' => $size,
                'n' => 1
            ]
        ];
        
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey,
            'X-DashScope-Async: enable'
        ];
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => $config['timeout'] ?? 60,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception('cURL错误: ' . $error);
        }
        
        if ($httpCode !== 200) {
            $errorData = json_decode($response, true);
            $errorMsg = $errorData['message'] ?? '未知错误';
            throw new Exception("API请求失败 (HTTP {$httpCode}): {$errorMsg}");
        }
        
        $result = json_decode($response, true);
        
        // 千问API返回任务ID，需要轮询获取结果
        if (isset($result['output']['task_id'])) {
            return $this->pollQianwenTask($result['output']['task_id'], $apiKey);
        } else if (isset($result['output']['results'][0]['url'])) {
            return $result['output']['results'][0]['url'];
        }
        
        throw new Exception('API响应格式错误');
    }
    
    /**
     * 轮询千问任务结果
     */
    private function pollQianwenTask($taskId, $apiKey)
    {
        $taskUrl = 'https://dashscope.aliyuncs.com/api/v1/tasks/' . $taskId;
        $maxAttempts = 30; // 最多轮询30次
        $attempt = 0;
        
        while ($attempt < $maxAttempts) {
            sleep(2); // 等待2秒
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $taskUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_HTTPHEADER => [
                    'Authorization: Bearer ' . $apiKey
                ],
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200) {
                $result = json_decode($response, true);
                
                if (isset($result['output']['task_status'])) {
                    if ($result['output']['task_status'] === 'SUCCEEDED') {
                        if (isset($result['output']['results'][0]['url'])) {
                            return $result['output']['results'][0]['url'];
                        }
                    } else if ($result['output']['task_status'] === 'FAILED') {
                        throw new Exception('图片生成失败: ' . ($result['output']['message'] ?? '未知错误'));
                    }
                }
            }
            
            $attempt++;
        }
        
        throw new Exception('图片生成超时，请稍后重试');
    }
    
    /**
     * 调用阿里千问文本生成API
     */
    private function callQianwenTextApi($prompt, $maxTokens, $config)
    {
        $apiKey = $config['qianwen']['api_key'] ?? '';
        $apiUrl = $config['qianwen']['text_api_url'] ?? 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';
        
        if (empty($apiKey)) {
            throw new Exception('阿里千问 API密钥未配置');
        }
        
        $data = [
            'model' => $config['qianwen']['text_model'] ?? 'qwen-turbo',
            'input' => [
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ]
            ],
            'parameters' => [
                'max_tokens' => $maxTokens,
                'temperature' => 0.7
            ]
        ];
        
        $headers = [
            'Authorization: Bearer ' . $apiKey,
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception('阿里千问 API请求失败，HTTP状态码: ' . $httpCode);
        }
        
        $result = json_decode($response, true);
        if (isset($result['output']['text'])) {
            return trim($result['output']['text']);
        }
        
        throw new Exception('阿里千问 API返回格式错误');
    }
    
    /**
     * 调用OpenAI图片生成API
     */
    private function callOpenAIImageApi($prompt, $size, $apiKey, $apiUrl, $model, $config)
    {
        $data = [
            'model' => $model,
            'prompt' => $prompt,
            'n' => 1,
            'size' => $size
        ];
        
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey
        ];
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => $config['timeout'] ?? 60,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception('cURL错误: ' . $error);
        }
        
        if ($httpCode !== 200) {
            $errorData = json_decode($response, true);
            $errorMsg = $errorData['error']['message'] ?? '未知错误';
            throw new Exception("OpenAI API请求失败 (HTTP {$httpCode}): {$errorMsg}");
        }
        
        $result = json_decode($response, true);
        
        if (isset($result['data'][0]['url'])) {
            return $result['data'][0]['url'];
        }
        
        throw new Exception('OpenAI API响应格式错误');
    }
    
    /**
     * 调用文本生成API
     */
    private function callTextGenerationApi($prompt, $maxTokens = 1000)
    {
        $config = config('ai.');
        $provider = $config['default_provider'] ?? 'qianwen';
        
        if ($provider === 'qianwen') {
            return $this->callQianwenTextApi($prompt, $maxTokens, $config);
        } else if ($provider === 'openai') {
            return $this->callOpenAITextApi($prompt, $maxTokens, $config);
        }
        
        throw new Exception('不支持的AI服务提供商: ' . $provider);
    }
    
    /**
     * 调用OpenAI文本生成API
     */
    private function callOpenAITextApi($prompt, $maxTokens, $config)
    {
        $apiKey = $config['openai']['api_key'] ?? '';
        $apiUrl = $config['openai']['text_api_url'] ?? 'https://api.openai.com/v1/chat/completions';
        
        if (empty($apiKey)) {
            throw new Exception('OpenAI API密钥未配置');
        }
        
        $data = [
            'model' => $config['openai']['text_model'] ?? 'gpt-3.5-turbo',
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'max_tokens' => $maxTokens,
            'temperature' => 0.7
        ];
        
        $headers = [
            'Authorization: Bearer ' . $apiKey,
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception('OpenAI API请求失败，HTTP状态码: ' . $httpCode);
        }
        
        $result = json_decode($response, true);
        if (isset($result['choices'][0]['message']['content'])) {
            return trim($result['choices'][0]['message']['content']);
        }
        
        throw new Exception('OpenAI API返回格式错误');
    }
    
    /**
     * 增强提示词
     */
    private function enhancePrompt($prompt, $style = 'default')
    {
        $stylePrompts = [
            'realistic' => '写实风格，高清，细节丰富',
            'cartoon' => '卡通风格，可爱，色彩鲜艳',
            'artistic' => '艺术风格，创意，抽象',
            'professional' => '专业风格，商务，简洁',
            'default' => '高质量，专业'
        ];
        
        $styleText = $stylePrompts[$style] ?? $stylePrompts['default'];
        return $prompt . '，' . $styleText;
    }
    
    /**
     * 下载并保存图片到本地
     */
    private function downloadAndSaveImage($imageUrl)
    {
        try {
            // 创建保存目录
            $uploadPath = ROOT_PATH . 'public' . DS . 'uploads' . DS . 'ai' . DS . date('Ymd') . DS;
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }
            
            // 生成文件名
            $fileName = 'ai_' . time() . '_' . uniqid() . '.png';
            $filePath = $uploadPath . $fileName;
            
            // 下载图片
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $imageUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            $imageData = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200 && $imageData) {
                // 保存图片
                if (file_put_contents($filePath, $imageData)) {
                    // 返回相对路径
                    return '/uploads/ai/' . date('Ymd') . '/' . $fileName;
                }
            }
            
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 测试AI连接
     */
    public function testConnection()
    {
        try {
            $config = config('ai.');
            $provider = $config['default_provider'] ?? 'qianwen';
            $providerConfig = $config[$provider] ?? [];
            
            if (empty($providerConfig['api_key'])) {
                $this->error('API密钥未配置');
            }
            
            // 测试简单的文本生成
            $testPrompt = '你好';
            $result = $this->callTextGenerationApi($testPrompt, 50);
            
            if ($result) {
                $this->success('连接测试成功', null, [
                    'provider' => $provider,
                    'response' => $result
                ]);
            } else {
                $this->error('连接测试失败');
            }
        } catch (\Exception $e) {
            $this->error('连接测试失败：' . $e->getMessage());
        }
    }
    
    public function index()
    {
        return $this->view->fetch('ai/ai/index');
    }

}
