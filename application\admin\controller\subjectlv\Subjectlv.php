<?php

namespace app\admin\controller\subjectlv;

use app\common\controller\Backend;
use fast\Tree;
/**
 * 
 *
 * @icon fa fa-file
 */
class Subjectlv extends Backend
{
    
    /**
     * Subjectlv模型对象
     */
    protected $model = null;
    protected $noNeedRight=["lists","status","class_type"];
    protected $noNeedLogin=["lists","status","class_type"];


    public function _initialize()
    {
        parent::_initialize();
        $this->shopid=false;
        $this->model = model('Subjectlv');
        $this->modelValidate = true;
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                ->where($where);
            $total = $total->where("status", "<>", -1);
            //if ($_SESSION['think']['admin']['group_id'] == 2) {
            //    $total = $total->where("city_id", "=", $_SESSION['think']['admin']['city_id']);
            //}
            $total = $total
                ->count();

            $list = $this->model
                ->where($where);
            //if ($_SESSION['think']['admin']['group_id'] == 2) {
            //    $list = $list->where("city_id", "=", $_SESSION['think']['admin']['city_id']);
            //}
            $list = $list->where("status", "<>", -1)
                ->order($sort, $order)
                ->limit($offset, 500)
                ->select();
            //$this->model->getLastSql();
            $list = collection($list)->toArray();

            $tree = Tree::instance();
            $tree->init($list, 'pid');
            $list = $tree->getTreeList($tree->getTreeArray(0), 'name');
            $data = array();
            for ($i = 0; $i < count($list); $i++) {
                $v = $list[$i];
                if($v['level']==0) {
                    $v['name'] = "<span class='lv1'>{$v['name']}</span>";
                }elseif($v['level']==1) {
                   // $v['name'] = "<span class='lv2'>{$v['name']}</span>";
                    $v['name'] = preg_replace_callback("/^(\&nbsp\;)((?:\s|├|\&nbsp\;|│|├|└)*?)(.*)$/Uisx",function ($em){
                        return "{$em[1]}".str_replace("&nbsp;","&nbsp;&nbsp;",$em[2])."<span class='lv2'>{$em[3]}</span>";
                    },$v['name']);
                }elseif($v['level']==2) {
                    $v['name'] = preg_replace_callback("/^(\&nbsp\;)((?:\s|\&nbsp\;|\│|\├|└)*?)(.*)$/Uisx",function ($em){
                        return "{$em[1]}".str_replace("&nbsp;","&nbsp;&nbsp;",$em[2])."<span class='lv3'>{$em[3]}</span>";
                    },$v['name']);
                }
                $data[] = [
                    'id' => $v['id'],
                    'parent' => $v['pid'] ? $v['pid'] : '#',
                    'name' => $v['name'],
                    'type' => $v['type'],
                    'addresslv_id' => $v['addresslv_id'],
                    'status' => $v['status'],
                    'school_id' => model("School")->getSchool_idTextAttr($v['school_id'])
                ];
            }

            for($i=0;$i<count($list);$i++){
//                $list[$i]['province_id']=model('Cityarea')->getProvince_idTextAttr($list[$i]['province_id']);
//                $list[$i]['city_id']=model('Cityarea')->getCity_idTextAttr($list[$i]['city_id']);
//                $list[$i]['district_id']=model('Cityarea')->getDistrict_idTextAttr($list[$i]['district_id']);
//                $list[$i]['province_id']=preg_replace("/\/$/","",model('Cityarea')->getProvince_idTextAttr($list[$i]['province_id'])."/".$list[$i]['city_id']."/".$list[$i]['district_id']);
//                $list[$i]['school_id']=model('School')->getSchool_idTextAttr($list[$i]['school_id'],$list[$i]);
                //$v['pid']=$this->model->getPidTextAttr($v['pid'],$v);
                $list[$i]['pid']=$this->model->getPidTextAttr($list[$i]['pid'],$list[$i]);
            }

            $result = array("total" => $total, "rows" => $data);
            return json($result);
        }
        $this->assignconfig("admin",$_SESSION['think']['admin']);
        return $this->view->fetch();
    }
    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        //$this->model->validate($validate);
                    }
                    $datatime=date("Y-m-d H:i:s");
                    $params['create_time']=$datatime;
                    $params['pid']=$params['subject_id'];
                    unset($params['delete_time']);
                    unset($params['update_time']);
//                    if($_SESSION['think']['admin']['group_id']==2){
//                        $params['city_id']=$_SESSION['think']['admin']['city_id'];
//                    }
                    $result = $this->model->allowField(true)->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($this->model->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->assignconfig('admin', $_SESSION['think']['admin']);
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = NULL)
    {
        $row = $this->model->get($ids);
        if (!$row)
            $this->error(__('No Results were found'));
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        //$row->validate($validate);
                    }
                    $datatime=date("Y-m-d H:i:s");
                    $params['update_time']=$datatime;
                    $params['pid']=$params['subject_id'];
                    unset($params['delete_time']);
                    unset($params['create_time']);
//                    if($_SESSION['think']['admin']['group_id']==2){
//                        $params['city_id']=$_SESSION['think']['admin']['city_id'];
//                    }
                    $result = $row->allowField(true)->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($row->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->assignconfig('admin', $_SESSION['think']['admin']);
        $this->view->assign("row", $row);
        return $this->view->fetch();

    }

    /**
     * 添加
     */
    public function view($ids = NULL)
    {
        $row=$this->model->getRow($ids);
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }


    public function lists($ids = NULL){
        $where="where 1=1";
//        if($_SESSION['think']['admin']['group_id']!=1){
//            $where.=" and city_id='{$_SESSION['think']['admin']['city_id']}'";
//        }
        $page=1;
        $pagesize=50;
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                $keyField = $this->request->request("keyField");
                $keyValue = $this->request->request("keyValue");
                $page = $this->request->request("pageNumber");
                $page = ($page == null) ? 1 : $page;
                $pagesize = $this->request->request("pageSize");
                $pagesize = ($pagesize == null) ? 10 : $pagesize;
                if ($keyValue != "") {
                    $nn=preg_split("/\,/",$keyValue);
                    for($i=0;$i<count($nn);$i++) {
                        $where.= " and $keyField='{$nn[$i]}'";
                    }
                }
            }else{
                $where="where status=1";
//                if(isset($_REQUEST['school_id'])&&$_REQUEST['school_id']>0){
//                    $where.=" and school_id=".$_REQUEST['school_id'];
//                }elseif($_SESSION['think']['admin']['school_id']){
//                    $where.=" and school_id=".$_SESSION['think']['admin']['school_id'];
//                }
                if(isset($_REQUEST['address_lv'])&&$_REQUEST['address_lv']>0){
                    $where.=" and addresslv_id=".$_REQUEST['address_lv'];
                    $r_name_address_lv = model("Addresslv")->field("id,name")->where("id", "=", $_REQUEST['address_lv'])->select();
                }
            }
        }

        $sql="select id,name name from dev002_gxyusheng.fa_subject_lv $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
        $list = db()->query($sql);
        if(isset($_REQUEST['school_id'])&&$_REQUEST['school_id']>0) {
            $r_name = model("Addresslv")->field("id,name")->where("id", "=", $_REQUEST['school_id'])->select();
            $school_id=$_REQUEST['school_id'];
        }else{
            $r_name = model("School")->field("id,name")->where("id", "=", $_SESSION['think']['admin']['school_id'])->select();
            $school_id=$_SESSION['think']['admin']['school_id'];
        }

        if(isset($r_name_address_lv)){
            $name=$r_name[0]['name'].$r_name_address_lv[0]['name']."学科";
        }else{
            $name=$r_name[0]['name']."学科";
        }

        if (empty($list)&&!empty($r_name)) {
            $data = array(
                "type" => "file",
                "pid" => 0,
                "name" => $name,
                "title" => $r_name[0]['name'],
                "ismenu" => 0,
                "createtime" => time(),
                "updatetime" => time(),
                "status" => 1,
                "school_id" => $school_id,
                "addresslv_id" => $_REQUEST['address_lv'],
            );
            $this->model->insertUpdate($data,["name","school_id","addresslv_id" ]);
        }
        $sql="select id,pid,name,title,status from dev002_gxyusheng.fa_subject_lv $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
        $list = db()->query($sql);
        $tree = Tree::instance();
        $tree->init($list, 'pid');
        $pid=$list[0]["id"];
        $list = $tree->getTreeList($tree->getTreeArray($pid), 'name');
        $data = array();
        for ($i = 0; $i < count($list); $i++) {
            $v = $list[$i];
            if(array_key_exists("level",$v)) {
                if ($v['level'] == 0) {
                    $v['name'] = "<span class='lv1'>{$v['name']}</span>";
                } elseif ($v['level'] == 1) {
                    $v['name'] = "<span class='lv2'>{$v['name']}</span>";
                } elseif ($v['level'] == 2) {
                    $v['name'] = "<span class='lv3'>{$v['name']}</span>";
                }
            }
            $data[] = [
                'id' => $v['id'],
                'parent' => $v['pid'] ? $v['pid'] : '#',
                'name' => $v['name'],
                //'level' => $v['level'],
                //'type' => $v['type'],
                'status' => $v['status'],
                'haschild' => $v['haschild'],
            ];
        }
        $total = db()->query("select count(1) as c from  dev002_gxyusheng.fa_subject_lv $where")[0];
        echo json_encode(array("list" => $list, "total" => $total['c']));
        exit;
    }

    public function class_type(){
            $where = "where 1=1";
            if ($_SESSION['think']['admin']['group_id'] == 2 || $_SESSION['think']['admin']['group_id'] == 8) {
                $where .= " and city_id='{$_SESSION['think']['admin']['city_id']}'";
            }
            $page = 1;
            $pagesize = 50;
            if ($this->request->isAjax()) {
                if ($this->request->request('keyField')) {
                    $keyField = $this->request->request("keyField");
                    $keyValue = $this->request->request("keyValue");
                    $page = $this->request->request("pageNumber");
                    $page = ($page == null) ? 1 : $page;
                    $pagesize = $this->request->request("pageSize");
                    $pagesize = ($pagesize == null) ? 10 : $pagesize;
                    if ($keyValue != "") {
                        $nn = preg_split("/\,/", $keyValue);
                        for ($i = 0; $i < count($nn); $i++) {
                            $where .= " and $keyField='{$nn[$i]}'";
                        }
                    }
                }
            }
            $sql = "select id,name name from dev002_gxyusheng.fa_subject_class_type $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
            $list = db()->query($sql);
            $total = db()->query("select count(1) as c from  dev002_gxyusheng.fa_subject_class_type $where")[0];
            echo json_encode(array("list" => $list, "total" => $total['c']));
            exit;


    }

    public function status($ids = '')
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }
        try {
            $result=$this->model->where("id","=",$ids)->field("status")->find();
            $v=$result->status==1?0:1;
            $this->model->where("id","=",$ids)->update(["status"=>$v]);
            $this->success('切换成功!');
        } catch (Exception $e) {
            $this->error('切换失败!'.$e->getMessage());
        }
    }

    public function changeStatus($ids = '')
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }
        try {
            $result=$this->model->where("id","=",$ids)->field("status")->find();
            $v=$result->status==1?0:1;
            $this->model->where("id","=",$ids)->update(["status"=>$v]);
            $this->success('切换成功!');
        } catch (Exception $e) {
            $this->error('切换失败!'.$e->getMessage());
        }
    }

}
