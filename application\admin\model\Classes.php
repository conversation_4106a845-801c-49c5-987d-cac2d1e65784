<?php

namespace app\admin\model;
use think\Model;
use fast\Tree;

class Classes extends Model
{
    // 表名
    public $name = 'classes';
    public $prefix_new = 'eb_';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    public $data;


    public function getCity_idTextAttr($value, $data)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("city")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    public function getClasses_idTextAttr($value, $data=[])
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("Classes")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    protected function setCity_idAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getArea_idTextAttr($value, $data)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("area")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    protected function setArea_idAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getSchool_idTextAttr($value, $data)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("school")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    protected function setSchool_idAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getTeaching_ocation_idTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getShow_in_teamTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "1"=>"是",
            "2"=>"否",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getIs_checkTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "1"=>"是",
            "2"=>"否",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getAllow_seeTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "1"=>"允许",
            "2"=>"不允许",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getClass_typeTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "1"=>"学校班级",
            "2"=>"社区班级",
            "3"=>"校外班级",
            "4"=>"组织机构班级",
            "5"=>"自建班级(个人创建的班级)",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getTypeTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "1"=>"学员",
            "2"=>"学校",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getIs_apply_to_joinTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "0"=>"否",
            "1"=>"是",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getCreator_typeTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "1"=>"老师或管理员",
            "2"=>"组织账号",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getOcation_idTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getProvice_idTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getDistrict_idTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getProvince_idTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getAddresslv_idTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getRel_typeTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getRel_idTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getCommunity_idTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

}