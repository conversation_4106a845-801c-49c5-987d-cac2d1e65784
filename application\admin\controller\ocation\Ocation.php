<?php

namespace app\admin\controller\ocation;

use app\common\controller\Backend;
use fast\Tree;
use think\Db;
use think\exception\PDOException;

/**
 *
 *
 * @icon fa fa-file
 */
class Ocation extends Backend
{

    /**
     * Ocation模型对象
     */
    protected $model = null;
    protected $noNeedRight = ["lists", "lists2", "status", "export2"];
    protected $noNeedLogin = ["lists", "lists2", "status", "export2"];


    public function _initialize()
    {
        parent::_initialize();
        $this->shopid = false;
        $this->model = model('Ocation');
        $this->modelValidate = true;
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        $relId = $this->request->get("rel_id")>0 ? $this->request->get("rel_id") : $_SESSION['think']['admin']['rel_id'];
        $relType = $this->request->get("rel_type")>0 ? $this->request->get("rel_type") : $_SESSION['think']['admin']['rel_type'];
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            $this->fixTeachingPointHierarchy();
            $where = "where 1=1";
            if (isset($relType) && ($relType == 1 || $relType == 2)) {
                $m = model("Addresslv")->where("rel_type", "=", $relType)
                    ->where("rel_id", "=", $relId)
                    ->where("status", "=", 1);
                if ($_SESSION['think']['admin']['group_id'] == 2 || $_SESSION['think']['admin']['group_id'] == 8) {
                    $m = $m->where("city_id", "=", $_SESSION['think']['admin']['city_id']);
                }
                if (isset($relType) && $relId > 0) {
                    $m = $m->where("rel_type", "=", $relType)->where("rel_id", "=", $relId);
                }
                $r = $m->select();

                if ($relType == "1" || $relType == "school") {
                    $tablename = "School";
                    $tableN = "eb_school";
                } else {
                    $tablename = "Community";
                    $tableN = "fa_community";
                }
                if ($relId != "") {
                    $r_name = model($tablename)->field("id,name")->where("id", "=", $relId)->select();
                    if (empty($r) && !empty($r_name)) {
                        $time = date("Y-m-d H:i:s");
                        $data = array(
                            "type" => "file",
                            "pid" => 0,
                            "name" => $r_name[0]['name'],
                            "title" => $r_name[0]['name'],
                            "ismenu" => 0,
                            "create_time" => $time,
                            "update_time" => $time,
                            "status" => 1,
                            "rel_id" => $relId,
                            "rel_type" => $relType,
                        );
                        model("Ocation")->insertUpdate($data, ["rel_id", "rel_type"]);
                    }
                }
                $m = model("Ocation")->where("rel_type", "=", $relType);
                if ($relId > 0) {
                    $m->where("rel_id", "=", $relId);
                }
                $data = $m->select();
                $tree = Tree::instance();
                $tree->init($data, 'pid');
                $pid = 0;
                $arr = $tree->getTreeList($tree->getTreeArray($pid), 'name');
                $data = array();
                for ($i = 0; $i < count($arr); $i++) {
                    $v = $arr[$i];
                    $entvirt = ($v['entvirt'] == "1") ? "实体" : "虚拟";
                    $endtime = empty($v['starttime']) ? '未设置' : substr($v['starttime'], 0, 10) . " 到 " .
                        (empty($v['endtime']) ? '未设置' : substr($v['endtime'], 0, 10));
                    $ocation_type = model("Ocation")->getOction_typeTextAttr($v['ocation_type']);
                    if (isset($_REQUEST['search'])) {
                        $_REQUEST["search"] = preg_replace("/党/", ".*", $_REQUEST["search"]);
                        if (isset($_REQUEST['search']) && $_REQUEST['search'] != "") {
                            if (!preg_match("/{$_REQUEST["search"]}/Uisx", $v['name'], $match)) {
                                continue;
                            }
                        }
                    }
                    if ($v['level'] == 0) {
                        $v['name'] = "<span class='lv1'>{$v['name']}</span>";
                    }
                    if ($v['level'] == 1) {
                        $v['name'] = preg_replace_callback("/^(\&nbsp\;)((?:\s|├|\&nbsp\;|│|├|└)*?)(.*)$/Uisx", function ($em) {
                            return "{$em[1]}" . str_replace("&nbsp;", "&nbsp;&nbsp;", $em[2]) . "<span class='lv2'>{$em[3]}</span>";
                        }, $v['name']);
                    }

                    if ($v['level'] == 2) {
                        $v['name'] = preg_replace_callback("/^(\&nbsp\;)((?:\s|\&nbsp\;|\│|\├|└)*?)(.*)$/Uisx", function ($em) {
                            return "{$em[1]}" . str_replace("&nbsp;", "&nbsp;&nbsp;", $em[2]) . "<span class='lv3'>{$em[3]}</span>";
                        }, $v['name']);
                    }

                    if ($v['level'] == 3) {
                        $v['name'] = preg_replace_callback("/^(\&nbsp\;)((?:\s|\&nbsp\;|\│|\├|└)*?)(.*)$/Uisx", function ($em) {
                            return "{$em[1]}" . str_replace("&nbsp;", "&nbsp;&nbsp;", $em[2]) . "<span class='lv3'>{$em[3]}</span>";
                        }, $v['name']);
                    }

                    if ($v['level'] == 4||$v['address_lv_octionlevel']==4) {
                        $v['name'] = preg_replace_callback("/^(\&nbsp\;)((?:\s|\&nbsp\;|\│|\├|└)*?)(.*)$/Uisx", function ($em) {
                            $em[3]=preg_replace("/lv[0-9]+/", "lv4", $em[3]);
                            return "{$em[1]}" . str_replace("&nbsp;", "&nbsp;&nbsp;", $em[2]) . "{$em[3]}";
                        }, $v['name']);
                        $v['name'] = preg_replace_callback("/^(\s*<span\s+class='lv[0-9]'>)((?:\s|\&nbsp\;|\│|├|├|└)*?)(.*)$/Uisx", function ($em) {
                            $em[1]=preg_replace("/lv[0-9]+/", "lv4", $em[1]);
                            return  str_replace("&nbsp;", "&nbsp;&nbsp;", $em[2]) . "{$em[1]}"."{$em[3]}";
                        }, $v['name']);

                    }
                    $data[] = [
                        'id' => $v['id'],
                        'parent' => $v['pid'] ? $v['pid'] : '#',
                        'name' => $v['name'],
                        'level' => $v['level'],
                        'type' => $v['type'],
                        'status' => $v['status'],
                        'haschild' => $v['haschild'],
                        'address' => $v['address'],
                        'weekdays' => $v['weekdays'],
                        'ocation_type' => $ocation_type,
                        'entvirt' => $entvirt,
                        'endtime' => $endtime,
                        'address_lv_octionlevel' => model("Ocation")->getAddress_lv_octionlevel_TextAttr($v['address_lv_octionlevel']),
                    ];
                }
            }
            $total = count($data);
            $result = array("total" => $total, "rows" => $data);
            return json($result);
        }
        $this->assignconfig("relType", $relType);
        $this->assignconfig("relId", $relId);
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        $relId = $this->request->get("relId") ? $this->request->get("relId") : $_SESSION['think']['admin']['rel_id'];
        $relType = $this->request->get("relType") ? $this->request->get("relType") : $_SESSION['think']['admin']['rel_type'];
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        //$this->model->validate($validate);
                    }
                    $datatime = date("Y-m-d H:i:s");
                    $params['create_time'] = $datatime;
                    unset($params['delete_time']);
                    unset($params['update_time']);
//                    if($_SESSION['think']['admin']['group_id']==2){
//                        $params['city_id']=$_SESSION['think']['admin']['city_id'];
//                    }
                    $params['pid'] = preg_replace("/\#/", "0", $params['pid']);
                    if ($params['address_lv_octionlevel'] == 1) {
                        unset($params['starttime']);
                        unset($params['endtime']);
                    }
                    $params['update_time'] = (!array_key_exists("update_time", $params) || $params['update_time'] == "") ? date("Y-m-d H:i:s", time()) : date("Y-m-d H:i:s", strtotime($params['update_time']));
                    $params['delete_time'] = (!array_key_exists("delete_time", $params) || $params['delete_time'] == "") ? date("Y-m-d H:i:s", time()) : date("Y-m-d H:i:s", strtotime($params['delete_time']));
                    $params['weekdays'] = join(",", $params['weekdays']);
                    $params['disable_week_range'] = join(",", $params['disable_week_range']);
                    $params['disable_week_time'] = join(",", $params['disable_week_time']);
                    $params['daytime'] = join(",", $params['daytime']);
                    $params['rel_id'] = $relId;
                    $params['rel_type'] = $relType;
                    $result = $this->model->allowField(true)->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($this->model->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->assign('admin', $_SESSION['think']['admin']);
        $this->assignconfig('admin', $_SESSION['think']['admin']);
        $this->assignconfig("relType", $relType);
        $this->assignconfig("relId", $relId);
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = NULL)
    {
        $row = $this->model->get($ids);
        $relId = $this->request->get("relId") ? $this->request->get("relId") : $_SESSION['think']['admin']['rel_id'];
        $relType = $this->request->get("relType") ? $this->request->get("relType") : $_SESSION['think']['admin']['rel_type'];
        if (!$row)
            $this->error(__('No Results were found'));
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        //$row->validate($validate);
                    }
                    $datetime = date("Y-m-d H:i:s");
                    $params['update_time'] = $datetime;
                    $params['schedule_data'] = json_decode($params['schedule_data'], 1);
                    $result = []; // 初始化结果数组
                    if (!empty($params['schedule_data'])) {
                        foreach ($params['schedule_data'] as $item) {
                            if ($item == null || empty($item['daytime']) || empty($item['oindex'])) {
                                continue; // 跳过当前元素
                            }
                            $key = $item['daytime'] . '_' . $item['oindex'];
                            $result[$key] = $item;
                        }
                        $params['schedule_data'] = json_encode(array_values($result));
                    }
                    unset($params['delete_time']);
                    unset($params['create_time']);
//                    if($_SESSION['think']['admin']['group_id']==2){
//                        $params['city_id']=$_SESSION['think']['admin']['city_id'];
//                    }
                    $params['create_time'] = (!array_key_exists("create_time", $params) || $params['create_time'] == "") ? date("Y-m-d H:i:s", time()) : date("Y-m-d H:i:s", strtotime($params['create_time']));
                    $params['delete_time'] = (!array_key_exists("delete_time", $params) || $params['delete_time'] == "") ? date("Y-m-d H:i:s", time()) : date("Y-m-d H:i:s", strtotime($params['delete_time']));
                    $params['pid'] = preg_replace("/\#/", "0", $params['pid']);
                    $params['weekdays'] = join(",", $params['weekdays']);
                    $params['disable_week_range'] = join(",", $params['disable_week_range']);
                    $params['disable_week_time'] = join(",", $params['disable_week_time']);
                    $params['daytime'] = join(",", $params['daytime']);
                    $params['rel_id'] = $relId;
                    $params['rel_type'] = $relType;
                    
                    // 检查日期时间字段，如果为空或address_lv_octionlevel=1则移除，避免SQL错误
                    if ($params['address_lv_octionlevel'] == 1 || empty($params['starttime'])) {
                        unset($params['starttime']);
                    }
                    if ($params['address_lv_octionlevel'] == 1 || empty($params['endtime'])) {
                        unset($params['endtime']);
                    }
                    $result = $row->allowField(true)->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($row->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->assign('admin', $_SESSION['think']['admin']);
        $this->assignconfig('admin', $_SESSION['think']['admin']);
        $this->assignconfig('relId', $relId);
        $this->assignconfig('relType', $relType);
        $this->view->assign("row", $row);
        return $this->view->fetch();

    }

    /**
     * 添加
     */
    public function view($ids = NULL)
    {
        $row = $this->model->getRow($ids);
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    public function lists($ids = NULL)
    {
        $where = "where 1=1";
        if ($_SESSION['think']['admin']['group_id'] != 1) {
            $where .= " and city_id='{$_SESSION['think']['admin']['city_id']}'";
        }
        if (isset($_REQUEST['addresslv_id']) && $_REQUEST['addresslv_id'] != "" && ($_SESSION['think']['admin']['group_id'] == "3" || $_SESSION['think']['admin']['group_id'] == "2")) {
            $where .= " and address_lv='{$_REQUEST['addresslv_id']}'";
        }
        if (isset($_REQUEST['addresslv_id']) && $_REQUEST['addresslv_id'] == "") {
            $where .= " and address_lv='{$_REQUEST['addresslv_id']}'";
        }
        if ($_SESSION['think']['admin']['group_id'] == 2 || $_SESSION['think']['admin']['group_id'] == 8) {
            $where .= " and city_id='{$_SESSION['think']['admin']['city_id']}'";
        }
        $relId = $this->request->get("rel_id") ? $this->request->get("rel_id") : $_SESSION['think']['admin']['rel_id'];
        $relType = $this->request->get("rel_type") ? $this->request->get("rel_type") : $_SESSION['think']['admin']['rel_type'];
        if (isset($relType) && isset($relId) > 0) {
            $where .= " and rel_id='{$relId}' and rel_type='{$relType}'";
        }
        $page = 1;
        $pagesize = 50;
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                $keyField = $this->request->request("keyField");
                $keyValue = $this->request->request("keyValue");
                $page = $this->request->request("pageNumber");
                $page = ($page == null) ? 1 : $page;
                $pagesize = $this->request->request("pageSize");
                $pagesize = ($pagesize == null) ? 10 : $pagesize;
                if ($keyValue != "") {
                    $nn = preg_split("/\,/", $keyValue);
                    for ($i = 0; $i < count($nn); $i++) {
                        $where .= " and $keyField='{$nn[$i]}'";
                    }
                }
            }
        }
        $sql = "select id,name name from fa_ocation $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
        $list = db()->query($sql);
        $total = db()->query("select count(1) as c from  fa_ocation $where")[0];
        echo json_encode(array("list" => $list, "total" => $total['c']), JSON_UNESCAPED_UNICODE);
        exit;
    }


    public function lists2($ids = NULL)
    {
        $where = "where 1=1";
        $relId = $this->request->get("relId") ? $this->request->get("relId") : $_SESSION['think']['admin']['rel_id'];
        $relType = $this->request->get("relType") ? $this->request->get("relType") : $_SESSION['think']['admin']['rel_type'];

        if (isset($relType)) {
            $m = model("Addresslv")->where("rel_type", "=",$relType)
                ->where("rel_id", "=", $relId)
                ->where("status", "=", 1);
            if ($_SESSION['think']['admin']['group_id'] == 2 || $_SESSION['think']['admin']['group_id'] == 8) {
                $m = $m->where("city_id", "=", $_SESSION['think']['admin']['city_id']);
            }
            if (isset($relType) && isset($relId) > 0) {
                $m = $m->where("rel_id", "=", $relId);
                $m = $m->where("rel_type", "=", $relType);
            }
            $r = $m->select();

            if ($relType == "1" || $_REQUEST['relType'] == "school") {
                $tablename = "School";
                $tableN = "eb_school";
            } else {
                $tablename = "Community";
                $tableN = "fa_community";
            }

            if ($relId != "") {
                if ($relId > 0) {
                    $m->where("rel_type", "=", $relType)->where("rel_id", "=", $relId);
                }
                $data = $m->select();
                $r_name = model("Addresslv")->field("id,name")->where("id", "=",$relId)->select();
                if (empty($data) && !empty($r_name)) {
                    $data = array(
                        "pid" => 0,
                        "name" => $r_name[0]['name'],
                        "title" => $r_name[0]['name'],
                        "ismenu" => 0,
                        "status" => 1,
                        "create_time" => date("Y-m-d H:i:s"),
                        "city_id" => $_SESSION['think']['admin']['city_id'],
                        "rel_id" => $relId,
                        "rel_type" => $relType,
                    );
                    model("Ocation")->create($data);
                }
            }
            $m = model("Ocation")->where("rel_type", "=", $relType);
            if ($relId > 0) {
                $m->where("rel_id", "=", $relId);
            }
            function week($week)
            {
                switch ($week) {
                    case "周一":
                        return 1;
                        break;
                    case "周二":
                        return 2;
                        break;
                    case "周三":
                        return 3;
                        break;
                    case "周四":
                        return 4;
                        break;
                    case "周五":
                        return 5;
                        break;
                    case "周六":
                        return 6;
                        break;
                    case "周日":
                        return 7;
                        break;
                }
                return 0;
            }

            function find_daytime($oindex, $schedule_data)
            {
                // 检查 $schedule_data 是否为数组
                if (!is_array($schedule_data)) {
                    return null; // 或者抛出异常
                }

                // 检查 $oindex 是否存在于 $schedule_data 中
                foreach ($schedule_data as $item) {
                    if ($item['oindex'] == $oindex) {
                        return $item["start_time"] . "-" . $item["end_time"];
                    }
                }
                return null;

            }

            $data = $m->select();
            $tree = Tree::instance();
            $tree->init($data, 'pid');
            $pid = 0;
            $arr = $tree->getTreeList($tree->getTreeArray($pid), 'name');

            $data = array();
            for ($i = 0; $i < count($arr); $i++) {
                $v = $arr[$i]->toarray();
                $schedule_data = isset($v['schedule_data']) ? json_decode($v['schedule_data'], 1) : [];
                $data2 = collection(model("Classroomeplan")->alias("r")->join("eb_classes c", "c.id=r.classes_id")->where("r.status", "1")->where("c.status", "1")->where("r.ocation_id", $v['id'])->select())->toArray();
                $newarr = array();
                foreach ($data2 as $k => $vv) {
                    $newarr[$v['id']][$vv['week']][] = $vv['daytime'];
                }
                $v['already_schedule'] = json_encode($newarr);
                if (!empty($schedule_data)) {
                    $newarr = array();
                    $nn33 = explode(",", $v["disable_week_time"]);
                    for ($j = 0; $j < count($nn33); $j++) {
                        $week2 = week(substr($nn33[$j], 0, 6));
                        $oindex = preg_replace("/节/", "", substr($nn33[$j], 7));
                        $daytime2 = find_daytime($oindex, $schedule_data);
                        $newarr[$v['id']][$week2][] = $daytime2;
                    }
                }
                $v['disable_week_time'] = json_encode($newarr);
                $data[] = [
                    'id' => $v['id'],
                    'parent' => $v['pid'] ? $v['pid'] : '#',
                    'name' => $v['name'],
                    'level' => $v['level'],
                    'type' => $v['type'],
                    'status' => $v['status'],
                    'haschild' => $v['haschild'],
                    'address_lv_octionlevel' => $v['address_lv_octionlevel'],
                    'schedule_data' => $v['schedule_data'],
                    'weekdays' => $v['weekdays'],
                    'already_schedule' => $v['already_schedule'],
                    'disable_week_time' => $v['disable_week_time'],
                ];
            }
        } elseif (isset($relType) && $relType == 2) {
            $where = "where 1=1";
            $m = model("Addresslv")->where("rel_type", "=", $relType)
                ->where("rel_id", "=", $relId)
                ->where("status", "=", 1);
            if ($_SESSION['think']['admin']['group_id'] == 2 || $_SESSION['think']['admin']['group_id'] == 8) {
                $m = $m->where("city_id", "=", $_SESSION['think']['admin']['city_id']);
            }
            if (isset($_SESSION['think']['admin']['rel_type']) && $_SESSION['think']['admin']['rel_id'] > 0) {
                $m = $m->where("rel_id", "=", $_SESSION['think']['admin']['rel_id']);
            }
            $r = $m->select();

            if ($relType == "1" || $relType == "school") {
                $tablename = "School";
                $tableN = "eb_school";
            } else {
                $tablename = "Community";
                $tableN = "fa_community";
            }

            if ($relId != "") {
                $m = model("Ocation")->where("rel_type", "=", $relType);
                if ($relId > 0) {
                    $m->where("rel_id", "=", $relId);
                }
                $data = $m->select();
                $r_name = model("Addresslv")->field("id,name")->where("id", "=", $relId)->select();
                if (empty($data) && !empty($r_name)) {
                    $data = array(
                        "pid" => 0,
                        "name" => $r_name[0]['name'],
                        "title" => $r_name[0]['name'],
                        "ismenu" => 0,
                        "status" => 1,
                        "create_time" => date("Y-m-d H:i:s"),
                        "city_id" => $_SESSION['think']['admin']['city_id'],
                        "rel_id" => $relId,
                        "rel_type" => $relType,
                    );
                    model("Ocation")->create($data);
                }
            }
            $m = model("Ocation")->where("rel_type", "=", $relType);
            if ($relId > 0) {
                $m->where("rel_id", "=", $relId);
            }
            $data = $m->select();
            $tree = Tree::instance();
            $tree->init($data, 'pid');
            $pid = 0;
            $arr = $tree->getTreeList($tree->getTreeArray($pid), 'name');

            $data = array();
            for ($i = 0; $i < count($arr); $i++) {
                $v = $arr[$i]->toarray();
                $schedule_data = isset($v['schedule_data']) ? json_decode($v['schedule_data'], 1) : [];
                $data2 = collection(model("Classroomeplan")->alias("r")->join("eb_classes c", "c.id=r.classes_id")->where("r.status", "1")->where("c.status", "1")->where("r.ocation_id", $v['id'])->select())->toArray();
                $newarr = array();
                foreach ($data2 as $k => $vv) {
                    $newarr[$v['id']][$vv['week']][] = $vv['daytime'];
                }
                $v['already_schedule'] = json_encode($newarr);
                if (!empty($schedule_data)) {
                    $newarr = array();
                    $nn33 = explode(",", $v["disable_week_time"]);
                    for ($j = 0; $j < count($nn33); $j++) {
                        $week2 = week(substr($nn33[$j], 0, 6));
                        $oindex = preg_replace("/节/", "", substr($nn33[$j], 7));
                        $daytime2 = find_daytime($oindex, $schedule_data);
                        $newarr[$v['id']][$week2][] = $daytime2;
                    }
                }
                $v['disable_week_time'] = json_encode($newarr);
                $data[] = [
                    'id' => $v['id'],
                    'parent' => $v['pid'] ? $v['pid'] : '#',
                    'name' => $v['name'],
                    'level' => $v['level'],
                    'type' => $v['type'],
                    'status' => $v['status'],
                    'haschild' => $v['haschild'],
                    'address_lv_octionlevel' => $v['address_lv_octionlevel'],
                    'schedule_data' => $v['schedule_data'],
                    'weekdays' => $v['weekdays'],
                    'already_schedule' => $v['already_schedule'],
                    'disable_week_time' => $v['disable_week_time'],
                ];
            }
        }
        $total = count($data);
        //$total = db()->query("select count(1) as c from  dev002_gxyusheng.{$tableN} $where")[0];
        echo json_encode(array("list" => $data, "total" => $total), JSON_UNESCAPED_UNICODE);
        exit;
    }


    public function status($ids = '')
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }
        try {
            $result = $this->model->where("id", "=", $ids)->field("status")->find();
            $v = $result->status == 1 ? 0 : 1;
            $this->model->where("id", "=", $ids)->update(["status" => $v]);
            $this->success('切换成功!');
        } catch (Exception $e) {
            $this->error('切换失败!' . $e->getMessage());
        }
    }

    public function changeStatus($ids = '')
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }
        try {
            $result = $this->model->where("id", "=", $ids)->field("status")->find();
            $v = $result->status == 1 ? 0 : 1;
            $this->model->where("id", "=", $ids)->update(["status" => $v]);
            $this->success('切换成功!');
        } catch (Exception $e) {
            $this->error('切换失败!' . $e->getMessage());
        }
    }

    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->where("pid", '<>', 0)->select();

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                $count += $item->delete();
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }

    public function export()
    {
        // 获取数据
        $list = $this->model->select();

        // 设置导出文件名
        $filename = 'your_filename_' . date('YmdHis');

        // 设置表头
        $headers = [
            'ID', 'Name', 'Create Time'
        ];

        // 设置数据
        $data = [];
        foreach ($list as $item) {
            $data[] = [
                $item['id'],
                $item['name'],
                $item['create_time'],
            ];
        }

        // 调用导出方法
        $this->exportExcel($filename, $headers, $data);
    }
    
    /**
     * 检查并修复教学点层级结构
     * 如果某个三级教学点或教室是直接从一级教学点继承的（缺少二级教学点）
     * 则在其之前插入一个二级教学点，并更新其父级关系
     */
    public function fixTeachingPointHierarchy()
    {
        // 设置过滤方法
        $this->request->filter(['strip_tags']);
        
        try {
            // 查找所有三级教学点或教室，它们的父级是一级教学点
            $problematicPoints = $this->model
                ->alias('o1')
                ->join('fa_ocation o2', 'o1.pid = o2.id')
                ->where('o1.address_lv_octionlevel', 'in', [3, 4]) // 三级教学点或教室
                ->where('o2.address_lv_octionlevel', '=', 1) // 父级是一级教学点
                ->field('o1.*, o2.name as parent_name, o2.id as parent_id')
                ->select();
            
            $count = 0;
            $processedParents = []; // 记录已处理过的一级教学点，避免重复创建二级教学点
            
            foreach ($problematicPoints as $point) {
                // 检查这个一级教学点是否已经创建了二级教学点
                if (isset($processedParents[$point['parent_id']])) {
                    $newLevel2Id = $processedParents[$point['parent_id']];
                } else {
                    // 检查是否已存在同名的二级教学点
                    $existingLevel2 = $this->model
                        ->where('pid', $point['parent_id'])
                        ->where('name', $point['parent_name'] . '中心教学点')
                        ->where('address_lv_octionlevel', 2)
                        ->find();
                    
                    if ($existingLevel2) {
                        $newLevel2Id = $existingLevel2['id'];
                    } else {
                        // 为一级教学点创建一个新的二级教学点
                        $newLevel2Point = [
                            'pid' => $point['parent_id'],
                            'name' => $point['parent_name'] . '中心教学点',
                            'title' => $point['parent_name'] . '中心教学点',
                            'address_lv_octionlevel' => 2, // 设置为二级教学点
                            'rel_id' => $point['rel_id'],
                            'rel_type' => $point['rel_type'],
                            'status' => 1,
                            'create_time' => date('Y-m-d H:i:s'),
                            'update_time' => date('Y-m-d H:i:s')
                        ];
                        
                        // 再次检查是否已存在相同的二级教学点（防止并发问题）
                        $existingLevel2Again = $this->model
                            ->where('pid', $point['parent_id'])
                            ->where('name', $point['parent_name'] . '中心教学点')
                            ->where('address_lv_octionlevel', 2)
                            ->find();
                            
                        if ($existingLevel2Again) {
                            // 如果在我们准备插入前已经有了相同的记录，就使用它
                            $newLevel2Id = $existingLevel2Again['id'];
                        } else {
                            // 插入新的二级教学点
                            $id=$this->model->insertUpdate($newLevel2Point,["name","rel_type","rel_id"]);
                            $newLevel2Id = $id;
                        }
                    }
                    
                    // 记录已处理过的一级教学点
                    $processedParents[$point['parent_id']] = $newLevel2Id;
                }
                
                // 检查当前教学点的父级是否已经是二级教学点
                if ($point['pid'] != $newLevel2Id) {
                    // 更新原三级教学点或教室的父级为新的二级教学点
                    $updateData = [
                        'pid' => $newLevel2Id
                    ];
                    
                    // 如果是三级教学点，将其改为教室（级别4）
                    if ($point['address_lv_octionlevel'] == 3) {
                        $updateData['address_lv_octionlevel'] = 4;
                    }
                    
                    // 使用原始方法进行更新
                    $updatePoint = $this->model->get($point['id']);
                    if ($updatePoint) {
                        $updatePoint->pid = $newLevel2Id;
                        if ($point['address_lv_octionlevel'] == 3) {
                            $updatePoint->address_lv_octionlevel = 4;
                        }
                        $result = $updatePoint->save();
                        if ($result !== false) {
                            $count++;
                        }
                    }
                }
            }
            // 返回成功结果，使用标准的 FastAdmin 成功响应格式
            return json(['code' => 1, 'msg' => '成功修复 ' . $count . ' 个教学点的层级结构', 'data' => null]);
        } catch (\Exception $e) {
            // 返回错误结果，使用标准的 FastAdmin 错误响应格式
            return json(['code' => 0, 'msg' => '修复失败: ' . $e->getMessage(), 'data' => null]);
        }
    }

    protected function exportExcel($filename, $headers, $data)
    {
        // 创建一个新的Spreadsheet对象
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // 添加标题，并合并A1到C1单元格，设置居中
        $title = 'Your Title Here';  // 替换为实际的标题文本
        $sheet->mergeCells('A1:C1');
        $sheet->setCellValue('A1', $title);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A1')->getFont()->setBold(true);

        // 设置表头
        $sheet->fromArray($headers, null, 'A2');

        // 设置数据
        $sheet->fromArray($data, null, 'A3');

        // 设置表头颜色为#ccc
        $sheet->getStyle('A2:C2')->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setARGB('FFCCCCCC');  // #ccc in ARGB format

        // 启用筛选功能
        $sheet->setAutoFilter('A2:C2');

        // 设置列宽为内容自适应
        foreach (range('A', $sheet->getHighestColumn()) as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        // 设置响应头
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
        header('Cache-Control: max-age=0');

        // 输出文件
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

}
