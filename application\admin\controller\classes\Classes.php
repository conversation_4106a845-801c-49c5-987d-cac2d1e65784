<?php

namespace app\admin\controller\classes;

use app\common\controller\Backend;
use fast\Tree;
use think\Db;
/**
 * 
 *
 * @icon fa fa-file
 */
class Classes extends Backend
{
    
    /**
     * Classes模型对象
     */
    protected $model = null;
    protected $noNeedRight=["lists","status",'lists2','classes_type','changestatus'];
    protected $noNeedLogin=["lists","status",'lists2','classes_type','changestatus'];


    public function _initialize()
    {
        parent::_initialize();
        $this->shopid=false;
        $this->model = model('Classes');
        $this->modelValidate = true;
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            
            // 构建总数查询
            $total = $this->model->alias('c')
                ->join("fa_course_table_autoplan_temp t","t.classes_id=c.id","LEFT")
                ->where("c.status", "=", 1)
                ->where("c.school_id", "=", $_SESSION['think']['admin']['school_id']);
            
            if (isset($_REQUEST['search']) && $_REQUEST['search'] != "") {
                $total = $total->where("c.name", "like", "%".$_REQUEST['search']."%");
            }
            
            if (isset($_REQUEST['rel_type'])){
                $total = $total->where("c.classes_type", intval($_REQUEST['rel_type']));
            }
            
            // 根据class_status参数筛选总数
            if(isset($_REQUEST['class_status'])){
                $today = date('Y-m-d');
                if($_REQUEST['class_status'] === '1' || $_REQUEST['class_status'] === 1){
                    // 正在进行中：无日期记录 或 最大日期大于等于当前日期
                    $total = $total->group("c.id")
                        ->having("(MAX(t.date) IS NULL OR DATE(MAX(t.date)) >= '$today')");
                }else{
                    // 已经结束：有日期记录 且 最大日期小于当前日期
                    $total = $total->group("c.id")
                        ->having("(MAX(t.date) IS NOT NULL AND DATE(MAX(t.date)) < '$today')");
                }
            }
            
            $total = $total->count();
            $this->assign('total', $total);
            
            // 构建列表查询
            $list = $this->model->alias('c')
                ->join("fa_course_table_autoplan_temp t","t.classes_id=c.id","LEFT")
                ->where("c.status", "=", 1)
                ->where("c.school_id", "=", $_SESSION['think']['admin']['school_id']);
            
            if (isset($_REQUEST['search']) && $_REQUEST['search'] != "") {
                $list = $list->where("c.name", "like", "%".$_REQUEST['search']."%");
            }
            
            if(isset($_REQUEST['rel_type'])){
                $list = $list->where("c.classes_type", intval($_REQUEST['rel_type']));
            }

            // 根据class_status参数筛选列表
            if(isset($_REQUEST['class_status'])){
                $today = date('Y-m-d');
                if($_REQUEST['class_status'] === '1' || $_REQUEST['class_status'] === 1){
                    // 正在进行中：无日期记录 或 最大日期大于等于当前日期
                    $list = $list->group("c.id")
                        ->field("c.*, MIN(t.date) as start_date, MAX(t.date) as end_datet.ocation_id,t.id t_id")
                        ->having("(MAX(t.date) IS NULL OR DATE(MAX(t.date)) >= '$today')");
                }else{
                    // 已经结束：有日期记录 且 最大日期小于当前日期
                    $list = $list->group("c.id")
                        ->field("c.*, MIN(t.date) as start_date, MAX(t.date) as end_date,t.ocation_id,t.id t_id")
                        ->having("(MAX(t.date) IS NOT NULL AND DATE(MAX(t.date)) < '$today')");
                }
            } else {
                // 没有状态筛选时，仍然需要分组获取日期信息
                $list = $list->group("c.id")
                    ->field("c.*, MIN(t.date) as start_date, MAX(t.date) as end_date,t.ocation_id,t.id t_id");
            }
            
            $list = $list->order($sort, $order)
                ->limit($offset, $limit)
                ->select();
            
            // 将查询结果转换为数组
            $list = is_array($list) ? $list : ($list ? $list->toArray() : []);
            $this->model->getlastsql();
            for($i=0;$i<count($list);$i++){
                $list[$i]['subject_lv']=model("Subjectlv")->getSubject_lvTextAttr($list[$i]['subject_lv'],$list[$i]);
                $list[$i]['show_in_team']=$this->model->getShow_in_teamTextAttr($list[$i]['show_in_team'],$list[$i]);
                $list[$i]['is_check']=$this->model->getIs_checkTextAttr($list[$i]['is_check'],$list[$i]);
                $list[$i]['allow_see']=$this->model->getAllow_seeTextAttr($list[$i]['allow_see'],$list[$i]);
                $list[$i]['class_type']=$this->model->getClass_typeTextAttr($list[$i]['class_type'],$list[$i]);
                $list[$i]['type']=$this->model->getTypeTextAttr($list[$i]['type'],$list[$i]);
                $list[$i]['leader_id']=model("User")->getUser_idTextAttr($list[$i]['leader_id'],$list[$i]);
                $list[$i]['manager_id']=model("User")->getUser_idTextAttr($list[$i]['manager_id'],$list[$i]);
                $list[$i]['is_apply_to_join']=$this->model->getIs_apply_to_joinTextAttr($list[$i]['is_apply_to_join'],$list[$i]);
                $list[$i]['creator_type']=$this->model->getCreator_typeTextAttr($list[$i]['creator_type'],$list[$i]);
                $list[$i]['province_id']=$this->model->getProvice_idTextAttr($list[$i]['province_id'],$list[$i]);
                $list[$i]['addresslv_id']=model("Addresslv")->getAddresslv_idTextAttr($list[$i]['addresslv_id'],$list[$i]);
                $list[$i]['rel_id']=model("School")->getSchool_idTextAttr($list[$i]['rel_id'],$list[$i]);
                $list[$i]['community_id']=model("Community")->getCommunity_idTextAttr($list[$i]['community_id'],$list[$i]);
                $list[$i]['ocation_id']=model("Ocation")->getOcation_idTextAttr($list[$i]['ocation_id'],$list[$i]);
                $list[$i]['apply_num']=db::name("eb_apply_form_item")->where("classes_id",$list[$i]['id'])->where("status",1)->count();
                // 返回统计用的原始数据
                $list[$i]['chart_data'] = [
                    'apply_num' => $list[$i]['apply_num'], // 实际报名人数
                    'max_num' => $list[$i]['num']     // 班级最大容量
                ];
                $list[$i]['cover']=createImg($_SERVER["DOCUMENT_ROOT"]."/".$list[$i]["cover"],"220","220");

            }
            $result = array("total" => $total, "rows" => $list);
            return json($result);
        }
//        $this->assignconfig("relType",$_REQUEST['relType']);
//        $this->assignconfig("relId",$_REQUEST['relId']);
        $this->assignconfig("admin",$_SESSION['think']['admin']);
        return $this->view->fetch();
    }
    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        //$this->model->validate($validate);
                    }
                    //判断下拉菜单是否为空
                    if(isset($params['classes_type'])) {
                        if(empty($params['classes_type'])){
                            $this->error('请选择班级类型');
                        }
                    }
                    if(isset($params['school_id'])) {
                        if(empty($params['school_id'])){
                            $this->error('请选择学校');
                        }
                    }
                    if(isset($params['subject_lv'])) {
                        if(empty($params['subject_lv'])){
                            $this->error('课程名称');
                        }
                    }
                    //名称查重
                    $where = [
                        'name' => $params['name'],
                    ];
                    if ($this->model->where($where)->find()) {
                        $this->error('名称重复');
                    }
                    $datatime=date("Y-m-d H:i:s");
                    $params['create_time']=$datatime;
                    unset($params['delete_time']);
                    unset($params['update_time']);
                    if(isset($params['student_id'])) {
                        $params['student_id'] = join(",", $params['student_id']);
                    }
                    if(isset($params['manager_id'])) {
                        $params['manager_id']=join(",",$params['manager_id']);
                    }


                    $result = $this->model->allowField(true)->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($this->model->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->assignconfig("admin",$_SESSION['think']['admin']);
        $this->assign("admin",$_SESSION['think']['admin']);
        /*$data=array("args"=>[[],[], []]);
        $leftOptions = $this->generateOptions('/user/user/lists',$data);
        $leftOptions = ($leftOptions==null)?[]:$leftOptions["leftOptions"];
        $rightOptions = $this->generateOptions('/user/user/lists');
        $rightOptions = ($rightOptions==null)?[]:$rightOptions["list"];
        $this->assignconfig("leftOptions", $leftOptions);
        $this->assignconfig("rightOptions", $rightOptions);*/
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = NULL)
    {
        $row = $this->model->get($ids);
        if (!$row)
            $this->error(__('No Results were found'));
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                try {
                    //是否采用模型验证
                    $validate = new \app\admin\validate\Classes();
                    if (!$validate->check($params)) {
                        //$this->error($validate->getError());
                    }
                    //名称查重
                    $where = [
                        'name' => $params['name'],
                        'id' => ['neq', $ids],
                    ];
                    if ($this->model->where($where)->find()) {
                        $this->error('名称重复');
                    }
                    //判断下拉菜单是否为空
                    if(isset($params['classes_type'])) {
                        if(empty($params['classes_type'])){
                            $this->error('请选择班级类型');
                        }
                    }
                    if(isset($params['school_id'])) {
                        if(empty($params['school_id'])){
                            $this->error('请选择学校');
                        }
                    }
                    if(isset($params['subject_lv'])) {
                        if(empty($params['subject_lv'])){
                            $this->error('课程名称');
                        }
                    }
                    $datatime=date("Y-m-d H:i:s");
                    $params['update_time']=$datatime;
                    unset($params['delete_time']);
                    unset($params['create_time']);
                    if(isset($params['leader_id'])&&$params['leader_id']!="") {
                        $params['leader_id'] = join(",", $params['leader_id']);
                    }
                    if(isset($params['manager_id'])&&$params['manager_id']!="") {
                        $params['manager_id']=join(",",$params['manager_id']);
                    }
                    //$params['manager_id']=join(",",$params['manager_id']);
//                    if($_SESSION['think']['admin']['group_id']==2){
//                        $params['city_id']=$_SESSION['think']['admin']['city_id'];
//                    }
                    $result = $row->allowField(true)->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($row->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->assignconfig("admin",$_SESSION['think']['admin']);
        $this->view->assign("row", $row);
/*        $teachers=($row["leader_id"]!="")?explode(",",$row["leader_id"]):[];
        $data=array("args"=>[$teachers,[], []]);
        $leftOptions = $this->generateOptions('/user/user/lists',$data);
        $leftOptions = ($leftOptions==null)?[]:$leftOptions["leftOptions"];
        $rightOptions = $this->generateOptions('/user/user/lists');
        $rightOptions = ($rightOptions==null)?[]:$rightOptions["list"];
        $this->assignconfig("leftOptions", $leftOptions);
        $this->assignconfig("rightOptions", $rightOptions);*/
        return $this->view->fetch();

    }

    /**
     * 添加
     */
    public function view($ids = NULL)
    {
        $row=$this->model->getRow($ids);
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    public function lists($ids = NULL){
        $where="where 1=1";
        if($_SESSION['think']['admin']['group_id']!=1){
            $where.=" and city_id='{$_SESSION['think']['admin']['city_id']}'";
        }
        $page=1;
        $pagesize=50;
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                $keyField = $this->request->request("keyField");
                $keyValue = $this->request->request("keyValue");
                $page = $this->request->request("pageNumber");
                $page = ($page == null) ? 1 : $page;
                $pagesize = $this->request->request("pageSize");
                $pagesize = ($pagesize == null) ? 10 : $pagesize;
                if ($keyValue != "") {
                    $nn=preg_split("/\,/",$keyValue);
                    for($i=0;$i<count($nn);$i++) {
                        $where.= " and $keyField='{$nn[$i]}'";
                    }
                }
            }else{
                $where="where 1=1";
                if(isset($_REQUEST['city_id'])&&$_REQUEST['city_id']>0){
                    //$where.=" and city_id=".$_REQUEST['city_id'];
                }
            }
        }
        $sql="select id,name name from dev002_gxyusheng.classes $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
        $list = db()->query($sql);

//        $r_name = model("School")->field("id,name")->where("id", "=", $_REQUEST['school_id'])->select();
//        if (empty($list)&&!empty($r_name)) {
//            $data = array(
//                "type" => "file",
//                "pid" => 0,
//                "name" => $r_name[0]['name']."学科",
//                "title" => $r_name[0]['name'],
//                "ismenu" => 0,
//                "createtime" => time(),
//                "updatetime" => time(),
//                "status" => 1,
//                "school_id" => $_REQUEST['school_id'],
//            );
//            $this->model->create($data);
//        }
//        $sql="select id,pid,name,title,status from dev002_gxyusheng.classes $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
//        $list = db()->query($sql);
//        $tree = Tree::instance();
//        $tree->init($list, 'pid');
//        $list = $tree->getTreeList($tree->getTreeArray(0), 'name');

        $total = db()->query("select count(1) as c from  dev002_gxyusheng.classes $where")[0];
        echo json_encode(array("list" => $list, "total" => $total['c']));
        exit;
    }

    public function status($ids = '')
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }
        try {
            $result=$this->model->where("id","=",$ids)->field("status")->find();
            $v=$result->status==1?0:1;
            $this->model->where("id","=",$ids)->update(["status"=>$v]);
            $this->success('切换成功!');
        } catch (\Exception $e) {
            $this->error('切换失败!'.$e->getMessage());
        }
    }

    public function changeStatus($ids = '')
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }
        try {
            $result=$this->model->where("id","=",$ids)->field("status")->find();
            $v=$result->status==1?0:1;
            $this->model->where("id","=",$ids)->update(["status"=>$v]);
            $this->success('切换成功!');
        } catch (\Exception $e) {
            $this->error('切换失败!'.$e->getMessage());
        }
    }

    public function lists2($ids = NULL)
    {
        $where = "where status=1";
        $page = 1;
        $pagesize = 50;
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                $keyField = $this->request->request("keyField");
                $keyValue = $this->request->request("keyValue");
                $page = $this->request->request("pageNumber");
                $page = ($page == null) ? 1 : $page;
                $pagesize = $this->request->request("pageSize");
                $pagesize = ($pagesize == null) ? 10 : $pagesize;
                if ($keyValue != "") {
                    $nn = preg_split("/\,/", $keyValue);
                    for ($i = 0; $i < count($nn); $i++) {
                        $where .= " and $keyField='{$nn[$i]}'";
                    }
                }
            }
        }
        $sql="select id,name name from dev002_gxyusheng.classes $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
        $list = db()->query($sql);
        if ($_SESSION['think']['admin']['group_id'] == 3) {
            for ($i = 0; $i < count($list); $i++) {
                if($list[$i]['name'] != "学校班级"){
                    unset($list[$i]);
                }
            }
        }elseif ($_SESSION['think']['admin']['group_id'] == 8) {
            for ($i = 0; $i < count($list); $i++) {
                if($list[$i]['name'] != "社区班级"){
                    unset($list[$i]);
                }
            }
        }
        $total = db()->query("select count(1) as c from  dev002_gxyusheng.classes $where")[0];
        echo json_encode(array("list" => $list, "total" => $total['c']));
        exit;
    }

    /**
     * AI自动生成图片 - 重定向到AI控制器
     */
    public function generateAiImage()
    {
        // 重定向到AI控制器处理
        $aiController = new \app\admin\controller\ai\Ai();
        return $aiController->generateImage();
    }

}
