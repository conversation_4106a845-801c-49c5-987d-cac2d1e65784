<?php

namespace app\admin\model;
use think\Model;
use fast\Tree;

class Column extends Model
{
    // 表名
    public $name = 'column';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    public $data;


    public function getCityidTextAttr($value, $data)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("city")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['cityname'];
        }
        return join(",",$s);
    }

    protected function setCityidAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getSchoolidTextAttr($value, $data)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("school")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['shool_name'];
        }
        return join(",",$s);
    }

    protected function setSchoolidAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getPidTextAttr($value, $data)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("school")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['column'];
        }
        return join(",",$s);
    }

    protected function setPidAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getCity_idTextAttr($value, $data)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("city")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['column'];
        }
        return join(",",$s);
    }

    protected function setCity_idAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getSchool_idTextAttr($value, $data)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("school")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['column'];
        }
        return join(",",$s);
    }

    protected function setSchool_idAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

}