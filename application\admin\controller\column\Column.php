<?php

namespace app\admin\controller\column;

use app\common\controller\Backend;
use fast\Tree;
/**
 * 
 *
 * @icon fa fa-file
 */
class Column extends Backend
{
    
    /**
     * Column模型对象
     */
    protected $model = null;
    protected $noNeedRight=['lists'];
    protected $noNeedLogin=['lists'];


    public function _initialize()
    {
        parent::_initialize();
        $this->shopid=false;
        $this->model = model('Column');
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $where=[];
            $total = $this->model
                ->where($where)
                ->order($sort, $order)
                ->count();

            $list = $this->model
                ->where($where)
                //->order("weigh desc")
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();
            //$this->model->getLastSql();
            $list = collection($list)->toArray();
            for($i=0;$i<count($list);$i++){
                $list[$i]['pid']=$this->model->getPidTextAttr($list[$i]['pid'],$list[$i]);
                $list[$i]['city_id']=model("Cityarea")->getCity_idTextAttr($list[$i]['city_id'],$list[$i]);
                $list[$i]['school_id']=model("School")->getSchool_idTextAttr($list[$i]['school_id'],$list[$i]);
                $list[$i]['type']=$list[$i]['type']==1?"是":"否";
                // 使用 ?? 空合并操作符提供默认值
                $list[$i]['diff']=($list[$i]['diff'] ?? 0)==0?"首页":"课堂";
                $list[$i]['status']=$list[$i]['status']==1?"生效中":"失效中";
            }
            $result = array("total" => $total, "rows" => $list);
            return json($result);
        }
        return $this->view->fetch();
    }
    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validate($validate);
                    }
                    $params['update_time']=(!array_key_exists("update_time",$params)||$params['update_time']=="")?date("Y-m-d H:i:s",time()):date("Y-m-d H:i:s",strtotime($params['update_time']));
                    $params['create_time']=(!array_key_exists("create_time",$params)||$params['create_time']=="")?date("Y-m-d H:i:s",time()):date("Y-m-d H:i:s",strtotime($params['create_time']));
                    $params['school_id'] = $this->auth->getUserInfo()['school_id'];
                    $result = $this->model->allowField(true)->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($this->model->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = NULL)
    {
        $row = $this->model->get($ids);
        if (!$row)
            $this->error(__('No Results were found'));
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validate($validate);
                    }
                    $params['update_time']=(!array_key_exists("update_time",$params)||$params['update_time']=="")?date("Y-m-d H:i:s",time()):date("Y-m-d H:i:s",strtotime($params['update_time']));
                    $params['create_time']=(!array_key_exists("create_time",$params)||$params['create_time']=="")?date("Y-m-d H:i:s",time()):date("Y-m-d H:i:s",strtotime($params['create_time']));
                    $result = $row->allowField(true)->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($row->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();

    }

    /**
     * 添加
     */
    public function view($ids = NULL)
    {
        $row=$this->model->getRow($ids);
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    public function lists($ids = NULL){
        $where="where 1=1";
        $page=1;
        $pagesize=50;
        
        // 添加type=1的过滤条件
//        $where .= " and type=1 or (1=1 ";
        $where .= " and type=1 ";

        // 添加school_id过滤条件（如果传递了school参数）
        $school_id = $this->request->request('school');
        if ($school_id) {
            $where .= " and school_id='{$school_id}'";
        }
//        $where .= ")";
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                $keyField = $this->request->request("keyField");
                $keyValue = $this->request->request("keyValue");
                $page = $this->request->request("pageNumber");
                $page = ($page == null) ? 1 : $page;
                $pagesize = $this->request->request("pageSize");
                $pagesize = ($pagesize == null) ? 10 : $pagesize;
                if ($keyValue != "") {
                    $nn=preg_split("/\,/",$keyValue);
                    $where.=" and (1=0";
                    for($i=0;$i<count($nn);$i++) {
                        $where.= " or $keyField='{$nn[$i]}'";
                    }
                    $where .= ")";
                }
            }
        }
        $sql="select id,`column` as name from dev002_gxyusheng.fa_column $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
        //echo $sql;
        $list = db()->query($sql);
    
        //$tree = Tree::instance();
        //$tree->init($list, 'pid');
        //if(isset($keyValue)&&$keyValue!=""){
        //}else{
        //    $list=$tree->getTreeList($tree->getTreeArray(0), 'name');
        //    //array_unshift($list,array("name"=>"无父级","pid"=>0,"weigh"=>9999));
        //}
    
        $total = db()->query("select count(1) as c from  dev002_gxyusheng.fa_column $where")[0];
        echo json_encode(array("list" => $list, "total" => $total['c']));
        exit;
    }

    function del($ids = null){
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }

            // 检查是否存在type=1的固定栏目
        $fixedColumns = $this->model->where('id', 'in', $ids)->where('type', 1)->count();
        if ($fixedColumns > 0) {
            $this->error('选中的记录中包含固定栏目，不可删除!');
        }
    
    
        try {
            // 物理删除
            $this->model->where('id', 'in', $ids)->where('type', '<>', 1)->delete();
            $this->success('删除成功!');
        } catch (Exception $e) {
            $this->error('删除失败!' . $e->getMessage());
        }
    }

}
