<?php

namespace app\admin\controller\community;

use app\common\controller\Backend;

/**
 * 公益课堂师资库管理
 *
 * @icon fa fa-users
 */
class Teacher extends Backend
{
    
    /**
     * Teacher模型对象
     */
    protected $model = null;
    protected $noNeedRight = ["lists", "status"];
    protected $noNeedLogin = ["lists", "status"];
    protected $searchFields = 'name,phone,specialty,introduction';
    protected $relationSearch = true;

    public function _initialize()
    {
        parent::_initialize();
        $this->shopid = false;
        $this->model = model('Teacher');
        $this->modelValidate = true;
        $this->view->assign("genderList", $this->model->getGenderList());
        $this->view->assign("educationList", $this->model->getEducationList());
        $this->view->assign("titleList", $this->model->getTitleList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            
            $user = $this->auth->getUserInfo();
            $community_id = isset($user['community_id']) ? $user['community_id'] : 0;
            
            $total = $this->model
                ->with(['community'])
                ->where($where)
                ->where(function($query) use ($community_id) {
                    if ($community_id > 0) {
                        $query->where('community_id', $community_id);
                    }
                })
                ->order($sort, $order)
                ->count();

            $list = $this->model
                ->with(['community'])
                ->where($where)
                ->where(function($query) use ($community_id) {
                    if ($community_id > 0) {
                        $query->where('community_id', $community_id);
                    }
                })
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();

            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);
            return json($result);
        }
        return $this->view->fetch();
    }
    
    /**
     * 获取教师列表
     */
    public function lists()
    {
        $community_id = $this->request->param('community_id', 0);
        $where = [];
        if ($community_id) {
            $where['community_id'] = $community_id;
        }
        $where['status'] = 1;
        
        $list = $this->model
            ->where($where)
            ->field('id, name, gender, avatar, title, specialty, teaching_years')
            ->order('weigh DESC, id DESC')
            ->select();
            
        return json(['code' => 1, 'msg' => '获取成功', 'data' => $list]);
    }
    
    /**
     * 导出教师信息
     */
    public function export()
    {
        $list = $this->model
            ->with(['community'])
            ->where('status', 1)
            ->order('weigh DESC, id DESC')
            ->select();
            
        $list = collection($list)->toArray();
        
        $header = [
            ['title' => '教师姓名', 'field' => 'name', 'width' => 15],
            ['title' => '性别', 'field' => 'gender_text', 'width' => 10],
            ['title' => '联系电话', 'field' => 'phone', 'width' => 15],
            ['title' => '电子邮箱', 'field' => 'email', 'width' => 20],
            ['title' => '学历', 'field' => 'education_text', 'width' => 10],
            ['title' => '职称', 'field' => 'title_text', 'width' => 10],
            ['title' => '专业特长', 'field' => 'specialty', 'width' => 30],
            ['title' => '教龄', 'field' => 'teaching_years', 'width' => 10],
            ['title' => '所属社区', 'field' => 'community.name', 'width' => 20],
        ];
        
        $filename = '公益课堂师资信息_' . date('YmdHis');
        
        return $this->exportExcel($filename, $header, $list);
    }
}