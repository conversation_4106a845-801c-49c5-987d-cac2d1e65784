<style>
    /* 选项卡样式 */
    .nav-tabs {
        border-bottom: 1px solid #ddd;
        margin-bottom: 20px;
    }

    .nav-tabs > li {
        float: left;
        margin-bottom: -1px;
    }

    .nav-tabs > li > a {
        margin-right: 2px;
        line-height: 1.42857143;
        border: 1px solid transparent;
        border-radius: 4px 4px 0 0;
        padding: 10px 15px;
    }

    .nav-tabs > li > a:hover {
        border-color: #eee #eee #ddd;
    }

    .nav-tabs > li.active > a,
    .nav-tabs > li.active > a:hover,
    .nav-tabs > li.active > a:focus {
        color: #555;
        cursor: default;
        background-color: #fff;
        border: 1px solid #ddd;
        border-bottom-color: transparent;
    }

    .tab-content {
        padding: 20px;
        border: 1px solid #ddd;
        border-top: none;
        background-color: #fff;
        margin-top: -20px;
    }

    /* 地图容器样式 */
    #map-container {
        width: 100%;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
</style>
<script>
    $(document).ready(function() {
        // 初始化选项卡
        $('.nav-tabs a').click(function(e) {
            e.preventDefault();
            $(this).tab('show');
        });

        // 初始化地图
        function initMap() {
            var map = new google.maps.Map(document.getElementById('map-container'), {
                center: {lat: 39.9042, lng: 116.4074}, // 默认中心点
                zoom: 8
            });
            // 这里可以添加地图标记等更多功能
        }

        // 加载Google Maps API
        if (document.getElementById('map-container')) {
            var script = document.createElement('script');
            script.src = 'https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap';
            script.async = true;
            document.head.appendChild(script);
        }
    });

</script>

<form id="edit-form" class="form-horizontal" style="position:relative;background: #fff" role="form"
      data-toggle="validator" method="POST" action="">

    <div class="panel-body">
        <!-- 选项卡导航 -->
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active">
                <a href="#basic-info" aria-controls="basic-info" role="tab" data-toggle="tab">基本信息</a>
            </li>
            <li role="presentation">
                <a href="#project-detail" aria-controls="project-detail" role="tab" data-toggle="tab">项目详情</a>
            </li>
            <li role="presentation">
                <a href="#class-info" aria-controls="class-info" role="tab" data-toggle="tab">班级情况</a>
            </li>
            <li role="presentation">
                <a href="#student-detail" aria-controls="student-detail" role="tab" data-toggle="tab">报名学员详情</a>
            </li>
            <li role="presentation">
                <a href="#registration-info" aria-controls="registration-info" role="tab" data-toggle="tab">报名情况</a>
            </li>
            <li role="presentation">
                <a href="#registration-summary" aria-controls="registration-summary" role="tab" data-toggle="tab">报名情况汇总</a>
            </li>
            <li role="presentation">
                <a href="#pre-registration-map" aria-controls="pre-registration-map" role="tab" data-toggle="tab">预报名地理位置分布图</a>
            </li>
        </ul>

        <!-- 选项卡内容 -->
        <div class="tab-content">
            <!-- 基本信息 -->
            <div role="tabpanel" class="tab-pane fade in active" id="basic-info">
                <!-- 原有所有表单内容 -->
                <input type="hidden" id="row" name="data" value=''/>
                <input type="hidden" id="id" name="row[id]" value=''/>
                <div class="form-group">
                    <label for="c-school_id" class="control-label col-xs-12 col-sm-2">学校:</label>
                    <div class="col-xs-12 col-sm-8" id="div-school_id">
                        <select name="row[school_id]" id="c-school_id" class="form-control">
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-apply_type" class="control-label col-xs-12 col-sm-2">报名类型:</label>
                    <div class="col-xs-12 col-sm-8" id="div-apply_type">
                        <label for="row_apply_type_0">
                            <input id="row_apply_type_0" name="row[apply_type]" type="radio" value="0" /> 预报名
                        </label>
                        <label for="row_apply_type_1">
                            <input id="row_apply_type_1" name="row[apply_type]" type="radio" value="1" /> 报名缴费
                        </label>
                    </div>
                </div>


                <div class="form-group">
                    <label for="c-table_id" class="control-label col-xs-12 col-sm-2">课程表:</label>
                    <div class="col-xs-12 col-sm-8" id="div-table_id">
                        <select name="row[table_id]" id="c-table_id" class="form-control">
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-title" class="control-label col-xs-12 col-sm-2">报名项目名称:</label>
                    <div class="col-xs-12 col-sm-8" id="div-title">
                        <input type="text" id="c-title" name="row[title]" value="" class="form-control"
                               title="title" placeholder="" data-rule="" data-tip="报名项目名称"/>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-intro" class="control-label col-xs-12 col-sm-2">课程介绍 :</label>
                    <div class="col-xs-12 col-sm-8" id="div-intro">
                        <textarea name="row[intro]" id="c-intro" class="form-control editor" data-rule="" rows="5"
                                  data-tip="课程介绍 "></textarea>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-cover" class="control-label col-xs-12 col-sm-2">封面 :</label>
                    <div class="col-xs-12 col-sm-8" id="div-cover">
                        <div class="form-inline">
                            <input id="c-cover" class="form-control" size="37" name="row[cover]" type="text"
                                   value="" data-tip="">
                            <span><button type="button" id="plupload-cover" class="btn btn-danger plupload"
                                          data-input-id="c-cover" data-mimetype="image/*" data-multiple="false"
                                          data-preview-id="p-cover"><i
                                    class="fa fa-upload"></i> {:__('Upload')}</button></span>
                            <span><button type="button" id="fachoose-cover" class="btn btn-primary fachoose"
                                          data-input-id="c-cover" data-mimetype="image/*" data-multiple="false"><i
                                    class="fa fa-list"></i> {:__('Choose')}</button></span>
                            <ul class="row list-inline plupload-preview" id="p-cover"></ul>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-amount" class="control-label col-xs-12 col-sm-2">学费 :</label>
                    <div class="col-xs-12 col-sm-8" id="div-amount">
                        <div class="form-inline">
                            <input id="c-amount" class="form-control" size="37" name="row[amount]" type="text"
                                   value="" data-tip="">
                            <ul class="row list-inline plupload-preview" id="p-amount"></ul>
                        </div>
                    </div>
                </div>


                <div class="form-group">
                    <label for="c-start_time" class="control-label col-xs-12 col-sm-2">开始时间:</label>
                    <div class="col-xs-12 col-sm-8" id="div-start_time">
                        <input type="text" id="c-start_time" name="row[start_time]" value=""
                               class="form-control datetimepicker" data-use-current="true"
                               data-date-format="YYYY-MM-DD HH:mm:ss" data-tip="开始时间" data-rule=""/>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-end_time" class="control-label col-xs-12 col-sm-2">结束时间 :</label>
                    <div class="col-xs-12 col-sm-8" id="div-end_time">
                        <input type="text" id="c-end_time" name="row[end_time]" value=""
                               class="form-control datetimepicker" data-use-current="true"
                               data-date-format="YYYY-MM-DD HH:mm:ss" data-tip="结束时间 " data-rule=""/>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-max_num" class="control-label col-xs-12 col-sm-2">招生人数限制:</label>
                    <div class="col-xs-12 col-sm-8" id="div-max_num">
                        <input type="number" id="c-max_num" name="row[max_num]" value="0" class="form-control"
                               title="max_num" data-rule="integer" placeholder="只能填入整数" data-tip="招生人数"/>
                    </div>
                </div>


                <!--是否允许积分兑换-->
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">是否允许积分兑换:</label>
                    <div class="col-xs-12 col-sm-8" id="div-is_use_integral">
                        <input id="c-is_use_integral" name="row[is_use_integral]" type="hidden" value="0">
                        <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-is_use_integral" data-yes="1" data-no="0">
                            <i class="fa fa-toggle-on text-success fa-2x"></i>
                        </a>
                        <div data-favisible="switch=1" class="p-3">是</div>
                        <div data-favisible="switch=0" class="p-3">否</div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">是否正式报名:</label>
                    <div class="col-xs-12 col-sm-8" id="div-is_real">
                        <input id="c-is_real" name="row[is_real]" type="hidden" value="0">
                        <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-is_real" data-yes="1" data-no="0">
                            <i class="fa fa-toggle-on text-success fa-2x"></i>
                        </a>
                        <div data-favisible="switch=1" class="p-3">是</div>
                        <div data-favisible="switch=0" class="p-3">否</div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-status" class="control-label col-xs-12 col-sm-2">状态:</label>
                    <div class="col-xs-12 col-sm-8" id="div-status">
                        <div class="btn-group" data-toggle="buttons">
                            <label class="btn btn-default">
                            <input type="radio" name="row[status]" value="0"> 未开启
                            </label>
                            <label class="btn btn-default">
                            <input type="radio" name="row[status]" value="1"> 已开启
                            </label>
                            <label class="btn btn-default">
                            <input type="radio" name="row[status]" value="2"> 已撤回
                            </label>
                        </div>
                    </div>
                </div>


                <div class="form-group">
                    <label for="c-is_close" class="control-label col-xs-12 col-sm-2">是否关闭:</label>
                    <div class="col-xs-12 col-sm-8" id="div-is_close">
                        <input id="c-is_close" name="row[is_close]" type="hidden" value="0">
                        <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-is_close" data-yes="1" data-no="0">
                            <i class="fa fa-toggle-on text-success fa-2x"></i>
                        </a>
                        <div data-favisible="switch=1" class="p-3">关闭</div>
                        <div data-favisible="switch=0" class="p-3">未关闭</div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-is_opened" class="control-label col-xs-12 col-sm-2">是否开启过:</label>
                    <div class="col-xs-12 col-sm-8" id="div-is_opened">
                        <input id="c-is_opened" name="row[is_opened]" type="hidden" value="0">
                        <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-is_opened" data-yes="1" data-no="0">
                            <i class="fa fa-toggle-on text-success fa-2x"></i>
                        </a>
                        <div data-favisible="switch=1" class="p-3">已开启</div>
                        <div data-favisible="switch=0" class="p-3">未开启</div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-is_finished" class="control-label col-xs-12 col-sm-2">是否结束:</label>
                    <div class="col-xs-12 col-sm-8" id="div-is_finished">
                        <input id="c-is_finished" name="row[is_finished]" type="hidden" value="0">
                        <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-is_finished" data-yes="1" data-no="0">
                            <i class="fa fa-toggle-on text-success fa-2x"></i>
                        </a>
                        <div data-favisible="switch=1" class="p-3">已结束</div>
                        <div data-favisible="switch=0" class="p-3">未结束</div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-is_user_end" class="control-label col-xs-12 col-sm-2">是否手动截止:</label>
                    <div class="col-xs-12 col-sm-8" id="div-is_user_end">
                        <input id="c-is_user_end" name="row[is_user_end]" type="hidden" value="0">
                        <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-is_user_end" data-yes="1" data-no="0">
                            <i class="fa fa-toggle-on text-success fa-2x"></i>
                        </a>
                        <div data-favisible="switch=1" class="p-3">是</div>
                        <div data-favisible="switch=0" class="p-3">否</div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-is_end" class="control-label col-xs-12 col-sm-2">是否截止:</label>
                    <div class="col-xs-12 col-sm-8" id="div-is_end">
                        <input id="c-is_end" name="row[is_end]" type="hidden" value="0">
                        <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-is_end" data-yes="1" data-no="0">
                            <i class="fa fa-toggle-on text-success fa-2x"></i>
                        </a>
                        <div data-favisible="switch=1" class="p-3">已截止</div>
                        <div data-favisible="switch=0" class="p-3">未截止</div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-dead_time" class="control-label col-xs-12 col-sm-2">截止时间 :</label>
                    <div class="col-xs-12 col-sm-8" id="div-dead_time">
                        <input type="text" id="c-dead_time" name="row[dead_time]" value=""
                               class="form-control datetimepicker" data-use-current="true"
                               data-date-format="YYYY-MM-DD HH:mm:ss" data-tip="截止时间 " data-rule=""/>
                    </div>
                </div>
                <!-- ... 原有所有表单内容 ... -->
                <div class="form-group layer-footer">
                    <label class="control-label col-xs-12 col-sm-2"></label>
                    <div class="col-xs-12 col-sm-8">
                        <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
                        <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                    </div>
                </div>
            </div>

            <!-- 项目详情 -->
            <div role="tabpanel" class="tab-pane fade" id="project-detail">
                <!-- 项目详情内容 -->
            </div>

            <!-- 班级情况 -->
            <div role="tabpanel" class="tab-pane fade" id="class-info">
                <!-- 班级情况内容 -->
            </div>

            <!-- 报名学员详情 -->
            <div role="tabpanel" class="tab-pane fade" id="student-detail">
                <!-- 报名学员详情内容 -->
            </div>

            <!-- 报名情况 -->
            <div role="tabpanel" class="tab-pane fade" id="registration-info">
                <!-- 报名情况内容 -->
            </div>

            <!-- 报名情况汇总 -->
            <div role="tabpanel" class="tab-pane fade" id="registration-summary">
                <!-- 报名情况汇总内容 -->
            </div>

            <!-- 预报名地理位置分布图 -->
            <div role="tabpanel" class="tab-pane fade" id="pre-registration-map">
                <div id="map-container" style="height: 500px;"></div>
            </div>
        </div>
    </div>
</form>
