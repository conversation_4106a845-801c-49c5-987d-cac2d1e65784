<form id="add-form" class="form-horizontal" style="position:relative;background: #fff" role="form" data-toggle="validator" method="POST" action="">
    
    <div class="panel-body">
        <div id="myTabContent" class="tab-content" >
            <div class="tab-pane fade active in" id="tabs_0">
                <input type="hidden" id="c-id" name="row[id]" value="" class="form-control" data-rule="" data-tip="ID"  />

                   <div class="form-group">
                        <label for="c-table_id" class="control-label col-xs-12 col-sm-2">课程或项目:</label>
                        <div class="col-xs-12 col-sm-8" id="div-table_id">
                            <input type="number" id="c-table_id" name="row[table_id]" value="" class="form-control" title="table_id" data-rule="integer" placeholder="只能填入整数"  data-tip="课程或项目"  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-classes_id" class="control-label col-xs-12 col-sm-2">班名:</label>
                        <div class="col-xs-12 col-sm-8" id="div-classes_id">
                           <select name="row[classes_id]" id="c-classes_id"  class="form-control"  >
                           </select>
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-ocation_id" class="control-label col-xs-12 col-sm-2">教室:</label>
                        <div class="col-xs-12 col-sm-8" id="div-ocation_id">
                           <select name="row[ocation_id]" id="c-ocation_id"  class="form-control"  >
                           </select>
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-subject_lv" class="control-label col-xs-12 col-sm-2">课程:</label>
                        <div class="col-xs-12 col-sm-8" id="div-subject_lv">
                            <input type="number" id="c-subject_lv" name="row[subject_lv]" value="" class="form-control" title="subject_lv" data-rule="integer" placeholder="只能填入整数"  data-tip="课程"  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-date" class="control-label col-xs-12 col-sm-2">日期:</label>
                        <div class="col-xs-12 col-sm-8" id="div-date">                          <div class="form-inline">
                                <input type="text"  id="c-date" name="row[date]" value="<?php echo date('Y-m-d');?>" class="form-control datetimepicker"  data-date-format="YYYY-MM-DD" data-tip="日期" data-rule=""  />
                            </div>
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-class_index" class="control-label col-xs-12 col-sm-2">第几节:</label>
                        <div class="col-xs-12 col-sm-8" id="div-class_index">
                            <input type="number" id="c-class_index" name="row[class_index]" value="" class="form-control" title="class_index" data-rule="integer" placeholder="只能填入整数"  data-tip="第几节"  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-count_total" class="control-label col-xs-12 col-sm-2">总课数:</label>
                        <div class="col-xs-12 col-sm-8" id="div-count_total">
                            <input type="number" id="c-count_total" name="row[count_total]" value="" class="form-control" title="count_total" data-rule="integer" placeholder="只能填入整数"  data-tip="总课数"  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-start_time" class="control-label col-xs-12 col-sm-2">上课时间:</label>
                        <div class="col-xs-12 col-sm-8" id="div-start_time">
                            <input type="text" id="c-start_time" name="row[start_time]" value="" class="form-control datetimepicker"  data-use-current="true"  data-date-format="YYYY-MM-DD HH:mm:ss" data-tip="上课时间" data-rule=""  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-end_time" class="control-label col-xs-12 col-sm-2">下课时间:</label>
                        <div class="col-xs-12 col-sm-8" id="div-end_time">
                            <input type="text" id="c-end_time" name="row[end_time]" value="" class="form-control datetimepicker"  data-use-current="true"  data-date-format="YYYY-MM-DD HH:mm:ss" data-tip="下课时间" data-rule=""  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-week" class="control-label col-xs-12 col-sm-2">周几:</label>
                        <div class="col-xs-12 col-sm-8" id="div-week">
                            <input type="text" id="c-week" name="row[week]" value="" class="form-control" title="week"  placeholder="" data-rule=""  data-tip="周几"  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-daytime" class="control-label col-xs-12 col-sm-2">时段:</label>
                        <div class="col-xs-12 col-sm-8" id="div-daytime">
                            <input type="text" id="c-daytime" name="row[daytime]" value="" class="form-control datetimepicker"  data-use-current="true"  data-date-format="YYYY-MM-DD HH:mm:ss" data-tip="时段" data-rule=""  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-oindex" class="control-label col-xs-12 col-sm-2">课节:</label>
                        <div class="col-xs-12 col-sm-8" id="div-oindex">
                            <input type="text" id="c-oindex" name="row[oindex]" value="" class="form-control" title="oindex"  placeholder="" data-rule=""  data-tip="课节"  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-teacher" class="control-label col-xs-12 col-sm-2">老师:</label>
                        <div class="col-xs-12 col-sm-8" id="div-teacher">
                            <input type="text" id="c-teacher" name="row[teacher]" value="" class="form-control" title="teacher"  placeholder="" data-rule=""  data-tip="老师"  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-bz" class="control-label col-xs-12 col-sm-2">备注:</label>
                        <div class="col-xs-12 col-sm-8" id="div-bz">
                            <input type="text" id="c-bz" name="row[bz]" value="" class="form-control" title="bz"  placeholder="" data-rule=""  data-tip="备注"  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-status" class="control-label col-xs-12 col-sm-2">状态:</label>
                        <div class="col-xs-12 col-sm-8" id="div-status">                    
                            <input id="c-status" name="row[status]" type="hidden" value="1">
                            <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-status" data-yes="1" data-no="0">
                                <i class="fa fa-toggle-on text-success fa-flip-horizontal fa-2x"></i>
                            </a>
                            <div data-favisible="switch=1" class="p-3">已开户</div>                        </div>
                   </div>
                <div class="form-group layer-footer">
                    <label class="control-label col-xs-12 col-sm-2"></label>
                    <div class="col-xs-12 col-sm-8">
                        <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
                        <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                    </div>
                </div>
            </div>
            
        </div>
    </div>
</form>



