<?php

namespace app\admin\model;
use think\Model;
use fast\Tree;

class Menu extends Model
{
    // 表名
    public $name = 'menu';
    public $prefix_new = 'fa_';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    public $data;

    public function insertUpdate($data, $uniqueColumns,$isUpdate=true)
    {
        if (array_key_exists("0",$data)) {
            foreach ($data as $key => $value) {
                $this->insertUpdate($data[$key], $uniqueColumns,$isUpdate);
            }
        } else {
            //构建查询条件
            $where = [];
            foreach ($uniqueColumns as $column) {
                if (isset($data[$column])) {
                    $where[$column] = $data[$column];
                } else {
                    // 可以根据实际情况处理错误，这里简单抛出异常
                    throw new \Exception("Unique column $column not found in data");
                }
            }
            // 先查询是否存在
            $record = $this->where($where)->find();
            if ($record) {
                if(!$isUpdate) return $record->id;
                // 存在则更新
                $record->save($data);
                $insertId = $record->id;
            } else {
                // 不存在则插入
                $record = $this->create($data);
                $insertId = $record->id;
            }
            return $insertId;
        }
    }
}