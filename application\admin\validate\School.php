<?php

namespace app\admin\validate;

use think\Validate;

class School extends Validate
{

    /**
     * 验证规则
     */
    protected $rule = [
        'province_id' => 'require',
        'city_id' => 'require',
        'district_id' => 'checkDistrictId',
        'name' => 'require|regex:.{8,40}|unique:school,name',
        'address' => 'require',
    ];

    protected function checkDistrictId($value, $rule, $data)
    {
        if ($data['city_id']== '0') {
            return true;
        }
        if($value==""||$value=="-1"){
            return false;
        }
        return true;
    }

    /**
     * 提示消息
     */
    protected $message = [
    ];

    /**
     * 字段描述
     */
    protected $field = [
        "province" => "省区",
        "city_id" => "城市",
        "district_id" => "区县",
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'add' => [
            'province_id' => 'require',
            'city_id' => 'require',
            'district_id' => 'checkDistrictId',
            'name' => 'require|regex:.{8,40}|unique:school,name',
            'address' => 'require',
        ],
        'edit' => [
            'province_id' => 'require',
            'city_id' => 'require',
            'district_id' => 'checkDistrictId',
            'name' => 'require|regex:.{8,40}',
            'address' => 'require',
        ]
    ];

    protected $validateScene = [

    ];

    public function __construct(array $rules = [], $message = [], $field = [])
    {
        $this->field = [
            'name' => "学校名称",
        ];
        $this->message = array_merge($this->message, [
            'name.regex' => '学校名称长度出错',
            'province_id.require' => '省份必选',
            'city_id.require' => '城市必选',
            'district_id.checkDistrictId' => '区县必选',
            'name_edit' => '学校名称不符合规范',
            'address.require' => '地址必填',
        ]);
        parent::__construct($rules, $message, $field);
    }

}
