<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-code" data-rule="required" class="form-control selectpicker" name="row[code]">
                <option value="">{:__('Please select')}</option>
                {foreach name="courseList" item="vo" key="k"}
                <option value="{$k}" {if $k==$row.code}selected{/if}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Community_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-community_id" data-rule="required" class="form-control selectpicker" name="row[community_id]">
                {foreach name="communityList" item="vo" key="k"}
                <option value="{$k}" {if $k==$row.community_id}selected{/if}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Publicwelfare_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-publicwelfare_id" data-rule="required" class="form-control selectpicker" name="row[publicwelfare_id]">
                {foreach name="publicwelfareList" item="vo" key="k"}
                <option value="{$k}" {if $k==$row.publicwelfare_id}selected{/if}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>

    <div class="form-group">
        <label for="c-cover" class="control-label col-xs-12 col-sm-2">班级封面:</label>
        <div class="col-xs-12 col-sm-8" id="div-cover">
            <div class="form-inline">
                <input id="c-cover" class="form-control" size="38" name="row[cover]" type="text"
                       value="{$row.cover}" data-tip="">
                <span><button type="button" id="plupload-cover" class="btn btn-danger plupload"
                              data-input-id="c-cover" data-mimetype="image/*" data-multiple="false"
                              data-preview-id="p-cover"><i
                        class="fa fa-upload"></i> {:__('Upload')}</button></span>
                <span><button type="button" id="fachoose-cover" class="btn btn-primary fachoose"
                              data-input-id="c-cover" data-mimetype="image/*" data-multiple="false"><i
                        class="fa fa-list"></i> {:__('Choose')}</button></span>
                <ul class="row list-inline plupload-preview" id="p-cover"></ul>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label for="c-intro" class="control-label col-xs-12 col-sm-2">班级介绍:</label>
        <div class="col-xs-12 col-sm-8" id="div-intro">
                        <textarea name="row[intro]" id="c-intro" class="form-control editor" data-rule="" rows="5"
                                  data-tip="班级介绍">{$row.intro}</textarea>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" class="form-control" name="row[remark]" rows="5">{$row.remark|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['1'=>__('Status 1'), '0'=>__('Status 0')], $row.status)}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weigh" class="form-control" name="row[weigh]" type="number" value="{$row.weigh}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
