<?php

namespace app\admin\controller\coursetable;

use app\admin\controller\subjectlv\Subjectlv;
use app\common\controller\Backend;
use app\admin\library\ClassesObject;
use app\admin\library\ClassplanObject;
use think\db;

use fast\Tree;

/**
 *
 *
 * @icon fa fa-file
 */
class Autoplan extends Backend
{

    /**
     * Autoplan模型对象
     */
    protected $model = null;
    protected $multiFields = 'status';
    protected $noNeedRight = ["lists", "status", "export", "export2", "redo", "checkFormalSchedule", "confirmSchedule", "clear"];
    protected $noNeedLogin = ["lists", "status", "export", "export2", "redo", "checkFormalSchedule", "confirmSchedule", "clear"];


    public function _initialize()
    {
        parent::_initialize();
        $this->shopid = false;
        $this->model = model('Autoplan');
        $this->modelValidate = true;
        
        // 修改为从fa_course_table表获取项目列表数据
        $tableList = Db::name('course_table')
            ->where('status', 1)
            ->field('id,name as project_name')  // 使用别名保持前端兼容性
            ->order('id desc')
            ->select();
        $this->view->assign('tableList', $tableList);
    }

    /**
     * 检查项目是否已有正式排课记录
     */
    public function checkFormalSchedule()
    {
        $publicwelfare_id = $this->request->param('table_id');
        
        if (empty($publicwelfare_id)) {
            $this->error('项目ID不能为空');
        }
        
        // 检查 fa_course_table_autoplan 表中是否存在该项目的记录
        $count = Db::name('course_table_autoplan')
            ->where('table_id', $publicwelfare_id)
            ->count();
        
        $this->success('', '', ['has_formal' => $count > 0]);
    }

    /**
     * 正式排课
     */
    public function confirmSchedule()
    {
        $table_id = $this->request->param('table_id');
        

        if (empty($table_id)) {
            \think\Log::error('confirmSchedule失败：项目ID为空');
            $this->error('项目ID不能为空');
        }
        
        Db::startTrans();
        try {
            // 检查是否已有正式排课数据
            $existCount = Db::name('course_table_autoplan')
                ->where('table_id', $table_id)
                ->count();
            

            if ($existCount > 0) {
                $deleteResult = Db::name('course_table_autoplan')
                    ->where('table_id', $table_id)
                    ->delete();
            }
            
            // 从临时表获取数据
            $tempData = Db::name('course_table_autoplan_temp')
                ->where('table_id', $table_id)
                ->select();

            
            if (empty($tempData)) {
                return json([
                    'code' => 0,
                    'msg' => '没有找到该项目的临时排课数据，请先进行自动排课',
                    'data' => null,
                    'url' => null,
                    'wait' => 3
                ]);
            }
            
            // 直接复制插入到正式表（两表结构相同）
            $insertData = [];
            foreach ($tempData as $item) {
                // 转换为数组格式并验证
                if (is_object($item)) {
                    $itemArray = $item->toArray();
                } elseif (is_array($item)) {
                    $itemArray = $item;
                } else {
                    continue; // 跳过无效数据
                }
                
                // 确保$itemArray是有效数组
                if (!is_array($itemArray)) {
                    continue;
                }
                
                // 移除临时表的id字段，其他字段直接复制
                if (isset($itemArray['id'])) {
                    unset($itemArray['id']);
                }
                $itemArray['create_time'] = time();
                $itemArray['update_time'] = time();
                $insertData[] = $itemArray;
            }
            

            $insertResult = Db::name('course_table_autoplan')->insertAll($insertData);
            Db::commit();
            $this->success('正式排课成功，共处理 ' . count($insertData) . ' 条记录');
            
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('正式排课失败：' );
        }
    }

    /**
     * 清空项目排期
     */
    public function clear()
    {
        $table_id = $this->request->param('table_id');
        

        if (empty($table_id)) {
             $this->error('项目ID不能为空');
        }
        
        Db::startTrans();
        try {
            // 检查临时表中是否有数据
            $count = Db::name('course_table_autoplan_temp')
                ->where('table_id', $table_id)
                ->count();

            if ($count == 0) {
                Db::commit();  // 先提交事务
                echo json_encode([
                    'code' => 1,
                    'msg' => '清空成功（临时表无数据）',
                    'data' => null,
                    'url' => null,
                    'wait' => 3
                ]);
                return;
            }
            
            // 清空fa_course_table_autoplan_temp表中table_id=选中项目ID的记录
            $deleteResult = Db::name('course_table_autoplan_temp')
                ->where('table_id', $table_id)
                ->delete();
            

            Db::commit();
            echo json_encode([
                'code' => 1,
                'msg' => '清空成功，共删除 ' . $deleteResult . ' 条记录',
                'data' => null,
                'url' => null,
                'wait' => 3
            ]);
            return;
            
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('清空失败：');
        }
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 解析禁用时段和禁用课节
     * @param string $disable_week_range 禁用时段范围
     * @param string $disable_week_time 禁用课节时间
     * @return array
     */
    private function parseDisableRanges($disable_week_range, $disable_week_time)
    {
        $disabled_ranges = [];
        $disabled_times = [];

        if (!empty($disable_week_range)) {
            $ranges = explode(',', $disable_week_range);
            foreach ($ranges as $range) {
                list($day, $time) = explode(' ', trim($range));
                $disabled_ranges[] = [$day, $time];
            }
        }

        if (!empty($disable_week_time)) {
            $times = explode(',', $disable_week_time);
            foreach ($times as $time) {
                list($day, $section) = explode(' ', trim($time));
                $disabled_times[] = [$day, intval(str_replace('节', '', $section))];
            }
        }

        return [$disabled_ranges, $disabled_times];
    }

    /**
     * 查看
     */
    public function index()
    {
        set_time_limit(300);
        $message = "";
        $infoss_in=[];
        
        // 在方法开始就定义weekdays变量，确保整个方法都能访问
        $weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        $daytimes = ['上午', '中午', '下午', '晚上'];
        // 初始化rows变量
        $rows = array();
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        $table_id=$this->request->request('table_id');
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $this->model = model("Autoplantemp")->alias("Autoplantemp");
            $total = $this->model
                ->where($where);
            $total = $total->where("status", "<>", -1);
            $total = $total->where("table_id", "=", $table_id);
            $total = $total
                ->count();
            $apply_form = Db::name('eb_apply_form')->where(['table_id' => $_REQUEST["table_id"]])->find();
            $apply_form_id=$apply_form['id']??0;
            if ($total == 0) {



                $this->model = model("Autoplantemp")->alias("Autoplantemp");
                list($where, $sort, $order, $offset, $limit) = $this->buildparams();
                $total = $this->model
                    ->join("eb_classes","eb_classes.id=Autoplantemp.classes_id")
                    ->field('Autoplantemp.id');
                // 添加课程ID条件
                if (isset($table_id) && $table_id != "") {
                    $total = $total->where("Autoplantemp.table_id", "=", $table_id);
                }
                if (isset($_REQUEST['search'])&&$_REQUEST['search'] != "") {
                    $total = $total->where("eb_classes.name", "like", "%".$_REQUEST['search']."%");
                }
                $total = $total
                    ->count();
                $list = $this->model->alias("Autoplantemp")
                    ->join("eb_classes","eb_classes.id=Autoplantemp.classes_id")
                    ->field('Autoplantemp.*');
                // 添加课程ID条件
                if (isset($table_id) && $table_id != "") {
                    $list = $list->where("Autoplantemp.table_id", "=", $table_id);
                }
                if (isset($_REQUEST['search'])&&$_REQUEST['search'] != "") {
                    $list = $list->where("eb_classes.name", "like", "%".$_REQUEST['search']."%");
                }
                //if ($_SESSION['think']['admin']['group_id'] == 2) {
                //    $list = $list->where("city_id", "=", $_SESSION['think']['admin']['city_id']);
                //}
                $list = $list->order('Autoplantemp.weigh desc,Autoplantemp.id')
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();
                $list = collection($list)->toArray();
                foreach ($list as $entry) {
                    $array = array();
                    $array['id'] = $entry['id'];
                    $array['table_id'] = model("Coursetable")->getCoursetable_idTextAttr($entry['table_id']);
                    $array['classes_id'] = model("Classes")->getClasses_idTextAttr($entry['classes_id']) . " (" . $entry['class_index'] . "/" . $entry['count_total'] . ")";
                    $array['ocation_id'] = model("Ocation")->getOcation_idTextAttr($entry['ocation_id']);
                    $array['subject_lv'] = model("Subjectlv")->getSubjectlv_idTextAttr($entry['subject_lv']);
                    $array['addresslv_id'] = model("User")->getUser_idTextAttr($entry['addresslv_id']);
                    $array['teacher_id'] = model("User")->getUser_idTextAttr($entry['teacher_id']);
                    $array['manager_id'] = model("User")->getUser_idTextAttr($entry['manager_id']);
                    $array['date'] = $entry['date'] . " " . $entry['start_time'] . "-" . $entry['end_time'];
                    $array['week'] = $entry['week'];
                    $array['daytime'] = $entry['daytime'];
                    $array['oindex'] = "该教室当天第" . preg_replace("/课|节/", "", $entry['class_index']) . "/" . $entry['count_total'] . "节课";
                    $array['bz'] = $entry['bz'];
                    $rows[] = $array;
                }

                $result = array("total" => $total, "rows" => $list);
                if ($total > 0) {
                    $result['rows'] = $list;
                } else {
                    $classesObject = new ClassesObject();
                    $classesObject->setTableId($table_id);
                    $classroomAvailability = $classesObject->classroomAvailability;
                    $classplanObject = new ClassplanObject();
                    $classplanObject->setTableId($table_id);
                    $r = model("Coursetable")->where("status", "=", 1)->where("id", "=", $table_id)->select();
                    $coursetable_name = $r[0]["name"];//课程项目名称
                    $classeseids = explode(",", $r[0]["classes_id"]);
                    $startDate = substr($r[0]['start_date'], 0, 10);
                    $endDate = "2028-01-01";

                    $r = model("Classplant")->where("status", "=", 1)->where("table_id", "=", $table_id)->where("classes_id", "in", $classeseids)->select();
                    $data = [];
                    for ($i = 0; $i < count($r); $i++) {
                        $row = $r[$i];
                        $classesname = model("Classes")->getClasses_idTextAttr($row["classes_id"]);
                        $data[$classesname] = array();
                        $data[$classesname]["class_id"] = $row["id"];
                        $data[$classesname]["classes_id"] = $row["classes_id"];
                        $data[$classesname]["type"] = model("Subjectlv")->getSubject_lvTextAttr($row["subject_lv"]);
                        $data[$classesname]["type"] = $data[$classesname]["type"] == "" ? "语言" : $data[$classesname]["type"];
                        $data[$classesname]["subject_lv"] = $row["subject_lv"];
                        $data[$classesname]["addresslv_id"] = $row["addresslv_id"];
                        $data[$classesname]["count"] = $row["ocourse_count"];
                        $data[$classesname]["count_total"] = $row["ocourse_count"];
                        $data[$classesname]["classroom_type"] = explode(",", $row["ocation_type"]);
                        $data[$classesname]["classroom_type"] = empty($data[$classesname]["classroom_type"]) || $data[$classesname]["classroom_type"][0] == "" ? ["1", "2", "3", "4"] : $data[$classesname]["classroom_type"];
                        $data[$classesname]["classroom_type"] = array_map(function ($v) {
                            return model("Ocation")->getOcation_typeTextAttr($v);
                        }, $data[$classesname]["classroom_type"]);
                        $data[$classesname]["allow_multiple_classes_per_day"] = $row["can_same_day"] ? true : false;
                        $data[$classesname]["fixed_classroom"] = $row["same_classroom"] ? true : false;
                        $data[$classesname]["disabled_times"] = week_daytime($row["disable_week_range"]);
                        $data[$classesname]["allowed_days"] = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                        $data[$classesname]["required_times"] = week_daytime($row["must_week_range"]);
                        $data[$classesname]["max_classes_per_week"] = $row["classes_per_week"];
                        $data[$classesname]["teacher"] = $row["teacher_id"];
                        $data[$classesname]["manager"] = $row["manager_id"];
                        $data[$classesname]["nearby_classrooms"] = [];//请设为空，因为不知道安排哪个教室
                    }
                    $courses = $data;

//从下开始排课================================================================================
                    $schedules = [];
                    $publicHolidays = [
                        '2025-01-01', // 元旦
                        '2025-02-10', // 春节
                        '2025-02-11', // 春节
                        '2025-02-12', // 春节
                        '2025-04-04', // 清明节
                        '2025-05-01', // 劳动节
                        '2025-06-18', // 端午节
                        '2025-09-17', // 中秋节
                        '2025-10-01', // 国庆节
                        '2025-10-02', // 国庆节
                        '2025-10-03', // 国庆节
                    ];


// 初始化排课表、教室占用情况和每周教室使用课节数
                    $schedule = [];
                    $classroomOccupancy = []; // 用于记录教室的时间占用情况
                    $weeklyClassroomUsage = []; // 用于记录每周教室的已使用课节数
                    $dailyClassUsage = []; // 用于记录每天每个班级的课程数
                    $classroomAssignment = []; // 用于记录每个班级的固定教室分配
                    $requiredTimesFailed = []; // 用于记录必排时间失败的情况
                    $weeklyClassUsage = [];  // 用于记录每周每个班级的已使用课节数
                    $teacherOccupancy = []; // 新增：用于记录老师的占用情况
                    $nearbyClassroomUsage = []; // 新增：用于记录临近教室的使用情况

// 定义英文星期到中文星期的映射
                    $weekdayMapping = [
                        'Monday' => '周一',
                        'Tuesday' => '周二',
                        'Wednesday' => '周三',
                        'Thursday' => '周四',
                        'Friday' => '周五',
                        'Saturday' => '周六',
                        'Sunday' => '周日'
                    ];

// 定义中文星期到英文星期的映射
                    $weekdayMappingReverse = array_flip($weekdayMapping);

// 初始化每个教室每天的使用情况
                    $classroomDailyUsage = [];

// 初始化必排时段的安排状态
                    $requiredTimesScheduled = [];

// 循环每一天
                    $period = [];
                    $currentDate = strtotime($startDate);
                    while ($currentDate <= strtotime($endDate)) {
                        $period[] = date('Y-m-d', $currentDate);
                        $currentDate += 86400;
                    }


                    // 学校的排课逻辑

                    foreach ($period as $date) {
                        $date = new \DateTime($date);
                        $dayOfWeek = $weekdayMapping[$date->format('l')];
                        $weekint = week2int($dayOfWeek);
                        $week = $weekint;
                        $dateString = $date->format('Y-m-d');
                        $weekKey = $date->format('Y-W'); // 用于标识当前周（年份 + 周数）

                        // 初始化每周教室使用课节数
                        if (!isset($weeklyClassroomUsage[$weekKey])) {
                            $weeklyClassroomUsage[$weekKey] = [];
                            foreach ($classroomAvailability as $classroom => $info) {
                                $weeklyClassroomUsage[$weekKey][$classroom] = 0;
                            }
                        }

                        // 初始化每周班级使用课节数
                        if (!isset($weeklyClassUsage[$weekKey])) {
                            $weeklyClassUsage[$weekKey] = [];
                            foreach ($courses as $class => $info) {
                                $weeklyClassUsage[$weekKey][$class] = 0;
                            }
                        }

                        // 跳过星期天和国定假日
                        if (in_array($dateString, $publicHolidays)) {
                            continue;
                        }

                        // 遍历每个教室
                        $exit_classroom = false;
                        foreach ($classroomAvailability as $classroom => $info) {
                            $c = $classroom;
                            $availability = $info['availability'];
                            $classroomType = $info['type']; // 教室类型
                            $maxLessonsPerWeek = $info['max_lessons_per_week']; // 每周最大课节数
                            $disabledTimes = $info['disabled_times'] ?? []; // 禁用时段
                            $disabledSlots = $info['disabled_slots'] ?? []; // 新增：禁用课节
                            $timeSlots = $info['time_slots']; // 时段划分
                            $classroomStart = new \DateTime($info['start']);
                            $classroomEnd = new \DateTime($info['end']);
                            $daytimeoindex = $info["daytimeoindex"];
                            $ocation_id = $info["classroom_id"];

                            // 检查当前日期是否在教室的使用期限内
                            if ($date >= $classroomStart && $date <= $classroomEnd) {
                                // 星期循环
                                if (isset($availability[$dayOfWeek])) {
                                    foreach ($availability[$dayOfWeek] as $timePeriod) {
                                        // 检查当前时段是否是禁用时段  $dayOfWeek周一$timePeriod上午
                                        if (isset($disabledTimes[$dayOfWeek]) && in_array($timePeriod, $disabledTimes[$dayOfWeek])) {
                                            continue; // 当前时段是禁用时段，跳过
                                        }

                                        // 遍历当前时段的课节
                                        if (array_key_exists($timePeriod, $timeSlots) && is_array($timeSlots[$timePeriod])) {
                                            //时段循环
                                            foreach ($timeSlots[$timePeriod] as $timeSlot) {
                                                // 检查当天课节是否是禁用课节 $dayOfWeek周一$timePeriod上午  $timeSlot课节1
                                                if (
                                                    (isset($disabledSlots[$dayOfWeek]) && in_array($timeSlot, $disabledSlots[$dayOfWeek])) ||
                                                    (isset($disabledSlots['*']) && in_array($timeSlot, $disabledSlots['*']))
                                                ) {
                                                    continue; // 当前课节是禁用课节，跳过
                                                }

                                                // 标记是否还有课程可排
                                                $hasCourseToSchedule = true;
                                                // 课程循环
                                                while ($hasCourseToSchedule) {
                                                    // 重置标记
                                                    $hasCourseToSchedule = false;


                                                    // 检查本周教室的课节数是否已达到上限
                                                    if ($weeklyClassroomUsage[$weekKey][$classroom] >= $maxLessonsPerWeek) {
                                                        break; // 本周课节数已满，跳出循环
                                                    }

                                                    // 初始化$class变量，避免未定义错误
                                                    $class = '';

                                                    // 遍历每个班级
                                                    foreach ($courses as $class => &$course) {
                                                        // 检查课程是否已经上完
                                                        if ($course['count'] <= 0) {
                                                            continue; // 课程已经上完，跳过该班级
                                                        }

                                                        $classes_id = $course["classes_id"];
                                                        $classplanObject->getClassPlansByClassId($course["classes_id"]);

                                                        // 确保$class变量已定义且不为空
                                                        if (!isset($class) || empty($class) || $class === '') {
                                                            continue;
                                                        }


                                                        // 检查教室在该课节是否已被占用
                                                        $key = $classroom . '-' . $dateString . '-' . $timeSlot;
                                                        //$key2= $classroomDailyUsage[$classroom][$dateString][$timePeriod . ' ' . $timeSlot];
                                                        if (isset($classroomOccupancy[$key])) {
                                                            break; // 课节已被占用，跳出循环
                                                        }

                                                        //是否计划课有安排该教室
                                                        $isplanclass = $classplanObject->hasOcationId($classes_id,$ocation_id);
                                                        // 检查教室类型是否匹配
                                                        $isinroomtype = in_array($classroomType, $course['classroom_type']);
                                                        $tags = [];
                                                        if ($isplanclass || $isinroomtype) {
                                                            if ($isplanclass && !$isinroomtype) {
                                                                $tags[] = "<span style='color:red'>被强行到安排不适合的教室类型，应安排".join(",",$course['classroom_type'])."类教室</span>";
                                                            }
                                                            // 检查是否允许同一天上两节课
                                                            if (!$course['allow_multiple_classes_per_day'] && isset($dailyClassUsage[$dateString][$class]) && $dailyClassUsage[$dateString][$class] >= 1) {
                                                                continue; // 不允许同个班同一天上两节课，跳过
                                                            }

                                                            // 检查是否需要固定教室
                                                            if ($course['fixed_classroom'] && isset($classroomAssignment[$class]) && $classroomAssignment[$class] != $classroom) {
                                                                continue; // 固定教室不匹配，跳过
                                                            }

                                                            // 检查班级是否禁用了当前时间段
                                                            if (isset($course['disabled_times'][$dayOfWeek]) && in_array($timePeriod, $course['disabled_times'][$dayOfWeek])) {
                                                                continue; // 班级禁用了当前时间段，跳过
                                                            }

                                                            // 检查班级是否只在特定天排课
                                                            if (!empty($course['allowed_days']) && !in_array($dayOfWeek, $course['allowed_days'])) {
                                                                continue; // 班级不在当前天排课，跳过
                                                            }

                                                            // 检查班级本周已排课节数是否已达到上限
                                                            if (isset($course['max_classes_per_week']) && $weeklyClassUsage[$weekKey][$class] >= $course['max_classes_per_week']) {
                                                                continue; // 本周已排课节数已达到上限，跳过
                                                            }


                                                            // 检查老师是否已被占用
                                                            $teacherKey = $course['teacher'] . '-' . $dateString . '-' . $timeSlot;
                                                            if($course['teacher']!="") {
                                                                if (isset($teacherOccupancy[$teacherKey])) {
                                                                    $infoss="{$class} 老师{$course['teacher']}在{$dateString}的{$timeSlot}已被占用，请重新安排<br>";
                                                                    if(!in_array($infoss,$infoss_in)){
                                                                        $infoss_in[]=$infoss;
                                                                        $message.=$infoss;
                                                                    }
                                                                    //continue; // 老师已被占用，跳过
                                                                }
                                                            }

                                                            // 检查临近教室是否已被占用
                                                            $nearbyKey = $timePeriod . '-' . $dateString;
                                                            if (empty($course['nearby_classrooms'])) {
                                                                $course['nearby_classrooms'] = getNearByClassroom($info['classroom_id']);
                                                            }

                                                            if (isset($nearbyClassroomUsage[$nearbyKey]) && !in_array($classroom, $course['nearby_classrooms'])) {
                                                                $infoss="{$class} {$course['teacher']}在{$dateString}的{$timeSlot}的临近教室已被占用，请重新安排<br>";
//                                                                if(!in_array($infoss,$infoss_in)){
//                                                                    $infoss_in[]=$infoss;
//                                                                    $message.=$infoss;
//                                                                }
                                                                //continue; // 当前教室不是临近教室，跳过
                                                            }

                                                            $info_oindex_data = getdaytimeOindexinfo($info, $timePeriod, $timeSlot);
                                                            $daytime = $info_oindex_data->start_time . "-" . $info_oindex_data->end_time;

                                                            $week = $weekint;
                                                            $info_2="{$class} 周{$week} {$daytime} 将安排在 {$classroom}";
                                                            $plan = $classplanObject->getplanByWeekAndDaytime($weekint, $daytime);
                                                            $hasroomplan = !empty($plan) ? true : false;
                                                            $infa = $course['classes_id'] . " " . $info["classroom_id"] . " " . $weekint . " " . $daytime;
                                                            if (!empty($plan)) {
                                                                $plfo = $plan["classes_id"] . " " . $plan["ocation_id"] . " " . $plan["week"] . " " . $daytime;
                                                            } else {
                                                                $plfo = "";
                                                            }
                                                            if ($class == "2024级银龄秋季太极1班" && $ocation_id == 85 && $week == 3) {
                                                                $bug = $course["type"] . $timePeriod . $timeSlot;
                                                            }
                                                            // 检查是否是计划排课或必排时间段
                                                            $isbipaisjduan = isset($course['required_times']) && !empty($course['required_times']) && isset($course['required_times'][$dayOfWeek]) && in_array($timePeriod, $course['required_times'][$dayOfWeek]);
                                                            if ($infa == $plfo || $isbipaisjduan) {
                                                                $tags[] = "计划排课或必排时间段";
                                                                // 必排时间段，优先排课
                                                                $schedule[] = [
                                                                    '班名' => $class,
                                                                    'class_id' => $course['class_id'],
                                                                    '教室' => $classroom . "({$info['type']})",
                                                                    'ocation_id' => $info["classroom_id"],
                                                                    '教室类型' => $info["type"],
                                                                    'subject_lv' => $course["subject_lv"],
                                                                    'address_lv' => $course["addresslv_id"],
                                                                    '课程' => model("Subjectlv")->getSubject_lvTextAttr($course["subject_lv"]),
                                                                    '日期' => $dateString,
                                                                    'date' => $dateString,
                                                                    '周几' => $dayOfWeek, // 添加周几字段
                                                                    '时段' => $timePeriod,
                                                                    '课节' => $timeSlot,
                                                                    '备注' => join('、', $tags),
                                                                    '老师' => $course['teacher'],
                                                                    '管理员' => $course['manager'],
                                                                    '课节合计' => count($info["daytimeoindex"]),
                                                                    'start_time' => $info_oindex_data->start_time,
                                                                    'end_time' => $info_oindex_data->end_time,
                                                                    'classes_id' => $course['classes_id'],
                                                                    'count_total' => $course['count_total'],
                                                                    'class_index' => $course['count_total'] - $course['count'] + 1
                                                                ];
                                                                $course['count']--;
                                                                $classroomOccupancy[$key] = true; // 标记该课节已被占用
                                                                $weeklyClassroomUsage[$weekKey][$classroom]++; // 增加本周教室的已使用课节数
                                                                $weeklyClassUsage[$weekKey][$class]++; // 增加本周班级的已使用课节数
                                                                $teacherOccupancy[$teacherKey] = true; // 标记老师已被占用
                                                                $nearbyClassroomUsage[$nearbyKey] = $classroom; // 标记临近教室已被占用

                                                                // 更新每天每个班级的课程数
                                                                if (!isset($dailyClassUsage[$dateString][$class])) {
                                                                    $dailyClassUsage[$dateString][$class] = 0;
                                                                }
                                                                $dailyClassUsage[$dateString][$class]++;

                                                                // 标记必排时段已安排
                                                                $requiredTimesScheduled[$class][$dateString] = true;

                                                                // 记录教室每天的使用情况
                                                                if (!isset($classroomDailyUsage[$classroom][$dateString])) {
                                                                    $classroomDailyUsage[$classroom][$dateString] = [];
                                                                }
                                                                $classroomDailyUsage[$classroom][$dateString][$timePeriod . ' ' . $timeSlot] = $class;
                                                                $classroomDailyUsage[$classroom][$dateString]['class'][] = $class;
                                                                $hasCourseToSchedule = true; // 标记还有课程可排
                                                            } elseif (isset($classplanObject->classesPlans) && !empty($classplanObject->classesPlans) && $classplanObject->classesPlans[0]['classes_per_week'] - $classplanObject->classesPlans[0]['planCount'] < 1) {
                                                                continue;
                                                            } else {
                                                                $info_2="{$class} 周{$week} {$daytime} 系统将安排在 {$classroom}";
                                                                // 非必排时间段，检查是否已安排必排时段
                                                                if (isset($course['required_times'][$dayOfWeek]) && !isset($requiredTimesScheduled[$class][$dateString])) {
                                                                    continue; // 必排时段未安排，跳过非必排时段
                                                                }

                                                                $info_oindex_data = getdaytimeOindexinfo($info, $timePeriod, $timeSlot);
                                                                // 非必排时间段，正常排课
                                                                $schedule[] = [
                                                                    '班名' => $class,
                                                                    'class_id' => $course['class_id'],
                                                                    '教室' => $classroom . "({$info['type']})",
                                                                    'ocation_id' => $info["classroom_id"],
                                                                    '教室类型' => $info["type"],
                                                                    'subject_lv' => $course["subject_lv"],
                                                                    'address_lv' => $course["addresslv_id"],
                                                                    '日期' => $dateString,
                                                                    'date' => $dateString,
                                                                    '周几' => $dayOfWeek, // 添加周几字段
                                                                    '时段' => $timePeriod,
                                                                    '课程' => model("Subjectlv")->getSubject_lvTextAttr($course["subject_lv"]),
                                                                    '课节' => $timeSlot,
                                                                    '课节合计' => count($info["daytimeoindex"]),
                                                                    '备注' => '系统自动排课',
                                                                    '老师' => $course['teacher'],
                                                                    '管理员' => $course['manager'],
                                                                    'classes_id' => $course['classes_id'],
                                                                    'start_time' => $info_oindex_data->start_time,
                                                                    'end_time' => $info_oindex_data->end_time,
                                                                    'count_total' => $course['count_total'],
                                                                    'class_index' => $course['count_total'] - $course['count'] + 1
                                                                ];
                                                                $course['count']--;
                                                                $classroomOccupancy[$key] = true; // 标记该课节已被占用
                                                                $weeklyClassroomUsage[$weekKey][$classroom]++; // 增加本周教室的已使用课节数

                                                                $weeklyClassUsage[$weekKey][$class]++; // 增加本周班级的已使用课节数
                                                                $teacherOccupancy[$teacherKey] = true; // 标记老师已被占用
                                                                $nearbyClassroomUsage[$nearbyKey] = $classroom; // 标记临近教室已被占用

                                                                // 更新每天每个班级的课程数
                                                                if (!isset($dailyClassUsage[$dateString][$class])) {
                                                                    $dailyClassUsage[$dateString][$class] = 0;
                                                                }
                                                                $dailyClassUsage[$dateString][$class]++;

                                                                // 记录教室每天的使用情况
                                                                if (!isset($classroomDailyUsage[$classroom][$dateString])) {
                                                                    $classroomDailyUsage[$classroom][$dateString] = [];
                                                                }
                                                                $classroomDailyUsage[$classroom][$dateString][$timePeriod . ' ' . $timeSlot] = $class;
                                                                $classroomDailyUsage[$classroom][$dateString]['class'][] = $class;
                                                                $hasCourseToSchedule = true; // 标记还有课程可排
                                                            }
                                                            // 分配固定教室
                                                            if ($course['fixed_classroom'] && !isset($classroomAssignment[$class])) {
                                                                $classroomAssignment[$class] = $classroom;
                                                            }
                                                        }
                                                        // 检查当天该教室的时段是否已经排满
                                                        if (isset($classroomDailyUsage[$classroom]) && isset($classroomDailyUsage[$classroom][$dateString]) && count($classroomDailyUsage[$classroom][$dateString]['class']) >= count($info["daytimeoindex"])) {
                                                            $exit_classroom = true;
                                                            break;// 该教室当天课节数已满，跳出班级循环，今天不再按排班级
                                                        }

                                                        //end 班级
                                                    }
                                                    if ($exit_classroom) break;
                                                    //end 课程
                                                }
                                                if ($exit_classroom) break;
                                                //end 时段
                                            }

                                        }
                                        if ($exit_classroom) break;
                                        //end 星期
                                    }
                                }
                            }else{
                                $infoss="{$class} 的教室在计划期内过期了<br>";
                                if(!in_array($infoss,$infoss_in)){
                                    $infoss_in[]=$infoss;
                                    $message.="{$class} 的教室在计划期内过期了<br>";
                                }
                            }
                            //end 教室
                        }
                    }


                    // 排课结果入库
                    if (!empty($schedule)) {
                        foreach ($courses as $class => $course) {
                            foreach ($course['required_times'] as $day => $times) {
                                foreach ($times as $time) {
                                    $found = false;
                                    foreach ($schedule as $entry) {
                                        if ($entry['班名'] == $class && $entry['周几'] == $day && $entry['时段'] == $time) {
                                            $found = true;
                                            break;
                                        }
                                    }
                                    if (!$found) {
                                        // 获取必排时间段的具体日期
                                        $requiredDate = new \DateTime($startDate);
                                        $dayOfWeek = $weekdayMappingReverse[$day];
                                        while ($requiredDate->format('l') !== $dayOfWeek) {
                                            $requiredDate->modify('next ' . $dayOfWeek);
                                        }
                                        if ($requiredDate < $startDate) {
                                            $requiredDate->modify('next ' . $dayOfWeek);
                                        }
                                        $requiredDateStr = $requiredDate->format('Y-m-d');
                                        $requiredTimesFailed[] = [
                                            '班名' => $class,
                                            '日期' => $requiredDateStr,
                                            '周几' => $day,
                                            '时段' => $time,
                                            '备注' => '必排时间段未排上',
                                        ];
                                    }
                                }
                            }
                        }
                    }

                    $rows2 = array();
                    foreach ($schedule as $entry) {
                        $array = array();
                        $array['table_type'] = 1;
                        $array['table_id'] = $_REQUEST["table_id"];
                        $array['classes_id'] = $entry['classes_id'];
                        $array['ocation_id'] = $entry['ocation_id'];
                        $array['subject_lv'] = $entry['subject_lv'];
                        $array['date'] = $entry['date'];
                        $array['start_time'] = $entry['start_time'];
                        $array['end_time'] = $entry['end_time'];
                        $array['week'] = $entry['周几'];
                        $array['daytime'] = $entry['时段'];
                        $array['teacher_id'] = $entry['老师'];
                        $array['manager_id'] = $entry['管理员'];
                        $array['status'] = 1;
                        $array['class_index'] = $entry['class_index'];
                        $array['oindex'] = preg_replace("/课|节/", "", $entry['课节']);
                        $array['oindex_count'] = $entry['课节合计'];
                        $array['count_total'] = $entry['count_total'];
                        $array['bz'] = $entry['备注'];
                        $array['apply_form_id'] = $apply_form_id;

                        // 获取班级信息以获取相关字段
                        $classInfo = model("Classes")->where("id", $entry['classes_id'])->find();

                        // 获取教学点信息以获取上级教学点ID
                        $ocationInfo = model("Ocation")->where("id", $entry['ocation_id'])->find();
                        $parentTeachingOcationId = $ocationInfo ? $ocationInfo['pid'] : 0;
                        $topocationInfo = model("Ocation")->where("id", $parentTeachingOcationId)->find();
                        $topTeachingOcationId = $topocationInfo ? $topocationInfo['pid'] : 0;
                        $areaTeachinocationInfo = model("Ocation")->where("id", $topTeachingOcationId)->find();
                        $areaTeachingOcationId = $areaTeachinocationInfo ? $areaTeachinocationInfo['pid'] : 0;

                        // 获取课程表信息以获取项目ID
                        $coursetableInfo = model("Coursetable")->where("id", $_REQUEST["table_id"])->find();

                        // 新增字段
                        $array['parent_teaching_ocation_id'] = $parentTeachingOcationId; // 上级教学点ID
                        $array['top_teaching_ocation_id'] = $topTeachingOcationId; // 顶级教学点ID
                        $array['area_teaching_ocation_id'] = $areaTeachingOcationId; // 社区教学点ID
                        $array['school_id'] = $classInfo ? $classInfo['school_id'] : 0; // 学校ID
                        $array['project_id'] = $coursetableInfo ? $coursetableInfo['id'] : 0; // 项目ID（使用课程表ID作为项目ID）
                        $array['community_id'] = $classInfo ? ($classInfo['community_id'] ?? 0) : 0; // 社区ID
                        $array['is_free'] = $classInfo ? ($classInfo['price']>0 ?0:1) : 0; // 是否免费
                        $updateData = [
                            'is_free' => $array['is_free'],
                            'community_id' => $array['community_id']
                        ];
                        
                        // 如果有教室ID，也更新ocation_id和parent_teaching_ocation_id
                        if (!empty($entry['ocation_id'])) {
                            $updateData['ocation_id'] = $entry['ocation_id'];
                            // 获取教室所属的教学点ID作为parent_teaching_ocation_id
                            if (isset($array['parent_teaching_ocation_id'])) {
                                $updateData['parent_teaching_ocation_id'] = $array['parent_teaching_ocation_id'];
                            }
                            if (isset($array['top_teaching_ocation_id'])) {
                                $updateData['top_teaching_ocation_id'] = $topTeachingOcationId;
                            }
                            if (isset($array['area_teaching_ocation_id'])) {
                                $updateData['area_teaching_ocation_id'] = $areaTeachingOcationId;
                            }
                        }
                        
                        // 更新班级表
                        Db::name('eb_classes')->where('id', $entry['classes_id'])->update($updateData);
                        
                        $rows2[] = $array;
                    }
                    $newdata = array();
                    $newdata['table_id'] = $_REQUEST["table_id"];
                    $newdata['json'] = json_encode($schedule);
                    model("Autoplantempjson")->insertUpdate($newdata, ['table_id'], true);
                    if (empty($rows2)) {
                        $this->error("排课失败，请检查必排时间段是否排满");
                    }
                    model("Autoplantemp")->insertUpdate($rows2, ['table_type','table_id', 'classes_id', 'ocation_id', 'date', 'start_time', 'class_index'], false);
                    $total = model("Autoplantemp")->where("table_id", "=", $_REQUEST["table_id"])
                        ->where("status", "<>", -1)
                        ->count();

                    $list = model("Autoplantemp")->where("table_id", "=", $_REQUEST["table_id"])
                        ->where("status", "<>", -1)
                        ->order('id')
                        ->limit($offset, $limit)
                        ->select();
                    $list = collection($list)->toArray();

                    foreach ($list as $entry) {
                        $array = array();
                        $array['id'] = $entry['id'];
                        $array['table_id'] = model("Coursetable")->getCourse_idTextAttr($entry['table_id']);
                        $array['classes_id'] = model("Classes")->getClasses_idTextAttr($entry['classes_id']) . " (" . $entry['class_index'] . "/" . $entry['count_total'] . ")";
                        $array['ocation_id'] = model("Ocation")->getOcation_idTextAttr($entry['ocation_id'],$entry);
                        $array['subject_lv'] = model("Subjectlv")->getSubjectlv_idTextAttr($entry['subject_lv']);
                        $array['date'] = $entry['date'] . " " . $entry['start_time'] . "-" . $entry['end_time'];
                        $array['week'] = $entry['week'];
                        $array['daytime'] = $entry['daytime'];
                        $array['oindex'] = "该教室当天第" . $entry['oindex'] . "/" . $entry['oindex_count'] . "节课";
                        $array['bz'] = $entry['bz'];
                        $rows[] = $array;
                    }
                }


                // 检查是否有未排的必排时间段

                if (!empty($requiredTimesFailed)) {
                    foreach ($requiredTimesFailed as $entry) {
                        $message .= "警告: " . $entry['班名'] . " 在 " . $entry['日期'] . " " . $entry['周几'] . " " . $entry['时段'] . " 未排上课。" . $entry['备注'] . "\n<br>";
                    }
                }

                // 检查是否有未排课的班级
                if (!empty($courses)) {
                    foreach ($courses as $class => $course) {
                        if ($course['count'] > 0) {
                            $message .= "警告: {$class} 还有 {$course['count']} 节课未安排。\n<br>";
                        }
                    }
                } else {
                    // 处理$courses为空的情况
                    $message .= "警告: 没有找到可安排的课程。\n<br>";
                }

                // 确保$class变量有默认值，避免后续使用时出现未定义错误
                if (!isset($class)) {
                    $class = '';
                }
                $daytimes = ['上午', '中午', '下午', '晚上'];


            }
            else{
                $list = model("Autoplantemp")->where("table_id", "=", $table_id)->select();
                $list = collection($list)->toArray();
                $rows=[];
                foreach ($list as $entry) {
                    $array = array();
                    $array['id'] = $entry['id'];
                    $array['table_id'] = model("Coursetable")->getCoursetable_idTextAttr($entry['table_id']);
                    $array['classes_id'] = model("Classes")->getClasses_idTextAttr($entry['classes_id']) . " (" . $entry['class_index'] . "/" . $entry['count_total'] . ")";
                    $array['ocation_id'] = model("Ocation")->getOcation_idTextAttr($entry['ocation_id']);
                    $array['subject_lv'] = model("Subjectlv")->getSubjectlv_idTextAttr($entry['subject_lv']);
                    $array['date'] = $entry['date'] . " " . $entry['start_time'] . "-" . $entry['end_time'];
                    $array['week'] = $entry['week'];
                    $array['daytime'] = $entry['daytime'];
//                    $array["teacher_id"] = model("User")->getUser_idTextAttr($entry['teacher_id']);
//                    $array["manager_id"] = model("User")->getUser_idTextAttr($entry['manager_id']);
                    $array['teacher_id'] = $entry['teacher_id'];
                    $array['manager_id'] = $entry['manager_id'];
                    $array['oindex'] = "该教室当天第" . $entry['oindex'] . "/" . $entry['oindex_count'] . "节课";
                    $array['bz'] = $entry['bz'];
                    $rows[] = $array;
                }
            }
            $message .= "<style>
#auto {
        border-collapse: collapse;
        width: 100%;
        margin-bottom: 20px;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }
    #auto .fixed-row th {
        background-color: #e8f5e9; /* 淡绿色背景 */
        color: #2e7d32; /* 深绿色文字 */
        font-weight: 600;
        padding: 12px;
        text-align: center;
        border-bottom: 2px solid #c8e6c9;
    }
    #auto th, #auto td {
        border: 1px solid #c8e6c9;
        padding: 10px;
        text-align: center;
        min-width: 100px;
        height: 30px;
    }
    #auto th.dblclick {
        cursor: pointer;
    }
    #auto tr:hover {
        background-color: #f1f8e9; /* 鼠标悬停时的淡绿色背景 */
    }
    #auto td {
        transition: background-color 0.3s ease;
    }
</style>";
            $data = model("Autoplantempjson")->where(['table_id' => $table_id])->find();
            $schedule = !empty($data["json"]) ? json_decode($data["json"], true) : []; // 获取排班数据
            $Ocations = model("Ocation")->select(); // 获取所有教室信息
            $ocationsClass = array();
            if (!empty($schedule)) {
                foreach ($schedule as $row) {
                    $row['教室'] = trim(preg_replace("/\((?:标准|肢体|书画|综合)\)/Uisx", "", $row['教室']));
                    $ocationsClass[$row['教室']][$row['周几']][$row['start_time']] = trim($row['班名']);
                }
            }

            // 开始构建排课表HTML
            $message .= "<table id='auto'>";
            // 添加表头行：教学点、教室、时间段和周一到周日
            $message .= "<tr class='fixed-row'><th class='dblclick'>教学点</th><th class='dblclick'>教室</th>";
            $message .= "<th>时间段</th>";
            // 添加周一到周日表头
            foreach ($weekdays as $day) {
                $message .= "<th>$day</th>";
            }
            $message .= "</tr>";
            
            // 处理教室数据，按父级ID分组排序
            $Ocations = collection($Ocations)->toArray();
            $Ocations = array_reverse($Ocations); // 反转数组，使排序更直观
            $newOcations = array();
            $oldpid = "";
            
            // 遍历所有教室，按父级ID分组
            foreach ($Ocations as $room) {
                // 初始化父级ID
                if ($oldpid == "") {
                    $oldpid = $room['pid'];
                }
                
                // 如果当前教室的父级ID与上一个相同，则加入同一组
                if ($room['pid'] == $oldpid) {
                    $samepid[] = $room;
                } else {
                    // 处理完一个父级ID的所有子项，进行排序
                    $oldpid = $room['pid'];
                    usort($samepid, function ($a, $b) {
                        if($a['id'] === $b['id']){
                            return 0;
                        }
                        return $a['id'] > $b['id']?1:-1;
                    });
                    
                    // 防止空数组
                    if (empty($samepid)) {
                        $samepid[] = $room;
                    }
                    
                    // 合并已排序的教室数据
                    $newOcations = array_merge($newOcations, $samepid);
                    $samepid = array($room);
                    
                    // 处理最后一个教室
                    if ($room['id'] == end($Ocations)['id']) {
                        usort($samepid, function ($a, $b) {
                            if($a['id'] === $b['id']){
                                return 0;
                            }
                            return $a['id'] > $b['id']?1:-1;
                        });
                        $newOcations = array_merge($newOcations, $samepid);
                        break;
                    }
                }
            }
            // 初始化上一个地址级别
            $oldaddresslv = "";
            
            // 遍历处理每个教室的排课信息
            foreach ($newOcations as $room) {
                // 跳过非教室级别的地址（如楼栋、楼层等）
                if ($room['address_lv_octionlevel'] < 4) {
                    continue;
                }
                
                // 获取教室所属地址名称（如"教学点A"）
                $addresslv_obj = model("Ocation")->get($room['pid']);
                $addresslv = $addresslv_obj['name'] ?? '';
                
                // 统计该地址下所有教室的排课时间段总数
                $addresslvsamecount = collection(model("Ocation")->where("pid", $room['pid'])->select())->toArray();
                $count = 0;
                foreach ($addresslvsamecount as $roomsamecount) {
                    if(isset($roomsamecount["schedule_data"]) && $roomsamecount["schedule_data"] != "") {
                        $scheduleData = json_decode($roomsamecount["schedule_data"], true) ?: [];
                        $count += count($scheduleData);
                    }
                }
                
                // 处理禁用时段和禁用课节
                list($disabled_ranges, $disabled_times) = $this->parseDisableRanges(
                    $room['disable_week_range'] ?? '', 
                    $room['disable_week_time'] ?? ''
                );
                
                // 解析教室的排课时间表数据
                $schedule2 = [];
                if (!empty($room['schedule_data'])) {
                    $schedule2 = json_decode($room['schedule_data'], true) ?: [];
                }
                if($count == 0) {
                    continue;
                }
                // 开始新行，显示教室地址（如果与上一行不同）
                $message .= "\n<tr>";

                if ($addresslv != $oldaddresslv) {
                    // 地址级别变化时，添加跨行单元格显示地址
                    $message .= "<td rowspan='" . $count . "'>{$addresslv}</td>";
                    $oldaddresslv = $addresslv;
                }
                
                // 遍历该教室的每个时间段
                $index = 1;
                foreach ($schedule2 as $time_name) {
                    // 如果是该教室的第一个时间段，显示教室名称（跨行合并）
                    if ($index == 1) {
                        $message .= "<td rowspan='" . count($schedule2) . "'>{$room['name']}</td>";
                    } else {
                        $message .= "\n <tr>";  // 新行（不包含教室地址和名称）
                    }
                    
                    // 显示时间段（如"08:00-09:30"）
                    $message .= "<td>{$time_name['start_time']}-{$time_name['end_time']}</td>";
                    
                    // 为周一到周日生成单元格
                    foreach ($weekdays as $day) {
                        $is_disabled_range = false;
                        
                        // 检查当前时段是否在禁用范围内
                        foreach ($disabled_ranges as $range) {
                            if ($range[0] == $day && $range[1] == $time_name) {
                                $is_disabled_range = true;
                                break;
                            }
                        }
                        
                        // 检查当前课节是否在禁用课节中
                        foreach ($disabled_times as $range) {
                            if ($range[0] == $day && $range[1] == $time_name["oindex"]) {
                                $is_disabled_range = true;
                                break;
                            }
                        }

                        // 设置单元格样式（禁用时段显示灰色背景）
                        $style = $is_disabled_range ? 'background-color: #ccc;' : '';
                        
                        // 获取该单元格的课程内容（如果有）
                        $cell_content = $ocationsClass[$room["name"]][$day][$time_name['start_time']] ?? '';
                        
                        // 输出单元格
                        $message .= "<td style='$style'>$cell_content</td>";
                    }
                    
                    // 结束当前行
                    $message .= "</tr>";
                    $index++;
                }
            }
            // 结束表格
            $message .= "</table>";

            // 准备返回的JSON数据
            $result["total"] = count($rows);      // 总记录数
            $result["message"] = $message;        // HTML格式的排课表
            $result["rows"] = $rows;              // 排课数据行

            // 返回JSON响应
            return json($result);
        }

//        $this->assignconfig("relType",$_REQUEST['relType']);
//        $this->assignconfig("relId",$_REQUEST['relId']);
        $this->assignconfig("relType", $_REQUEST['relType']);
        $this->assignconfig("table_id", $table_id);
        $this->assignconfig("admin", $_SESSION['think']['admin']);
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validate($validate);
                    }
                    $datatime = date("Y-m-d H:i:s");
                    $params['create_time'] = $datatime;
                    unset($params['delete_time']);
                    unset($params['update_time']);
                    //                    if($_SESSION['think']['admin']['group_id']==2){
                    //                        $params['city_id']=$_SESSION['think']['admin']['city_id'];
                    //                    }
                    $params['date'] = (!array_key_exists("date", $params) || $params['date'] == "") ? date("Y-m-d", time()) : date("Y-m-d", strtotime($params['date']));
                    $result = $this->model->allowField(true)->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($this->model->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->assignconfig("admin", $_SESSION['think']['admin']);
        $this->assign("rel_type", $_SESSION['think']['admin']['rel_type'] ?? '');
        $this->assign("rel_id", $_SESSION['think']['admin']['rel_id'] ?? '');

        // 生成 leftOptions 和 rightOptions 的数据
        $data = array("args" => [[], [], []]);
        $leftOptions = $this->generateOptions('/user/user/lists', $data);
        $leftOptions = ($leftOptions == null) ? [] : $leftOptions["leftOptions"];
        $rightOptions = $this->generateOptions('/user/user/lists');
        $rightOptions = ($rightOptions == null) ? [] : $rightOptions["list"];
        $this->assignconfig("leftOptions", $leftOptions);
        $this->assignconfig("rightOptions", $rightOptions);

        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public
    function edit($ids = NULL)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $row = model("Autoplantemp")->get($ids);
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        //$row->validate($validate);
                    }
                    $datetime = time();
                    $params['update_time'] = $datetime;
                    unset($params['delete_time']);
                    unset($params['create_time']);
//                    if($_SESSION['think']['admin']['group_id']==2){
//                        $params['city_id']=$_SESSION['think']['admin']['city_id'];
//                    }


                    $params['date'] = (!array_key_exists("date", $params) || $params['date'] == "") ? date("Y-m-d", time()) : date("Y-m-d", strtotime($params['date']));
                    $params['teacher_id'] = join(",", $params['teacher_id']);
                    $params['manager_id'] = join(",", $params['manager_id']);
                    unset($params['table_id']);//课程名称不需要改
                    unset($params['event_name']);//课程名称不需要改
                    unset($params['ocourse_logo']);//课程封面不需要改
                    unset($params['event_content']);//课程内容不需要改
                    unset($params['classes_id']);//班级不需要改
                    unset($params['course_type']);//课堂类型不需要改
                    unset($params['daytime']);//时段选择不需要改

                    $result = $row->allowField(true)->save($params);
                    //$result = $this->model->insertUpdate($params, ['id']);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($row->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        //项目课程事件
        $coursetable = model("Coursetable")->get($row['table_id']);
        $table_id_name = $coursetable['name'];
        $row['start_date'] = substr($coursetable['start_date'], 0, 10);
        $row['end_date'] = substr($coursetable['end_date'], 0, 10);
        $row['exclint_holidays'] = substr($coursetable['exclint_holidays'], 0, 10);
        //事件的日期$row['date'];
        //事件的日期$row['week'];
        //事件的日期课节开始时间$row['start_time'];
        //事件的日期课节结束时间$row['end_time'];
        //事件的日期课节,当天第几节课？ $row['class_index'];
        $time = new \DateTime($row['date'] . " " . $row['start_time']);
        $interval = new \DateInterval('PT30M');
        $time->sub($interval);
        //课前打卡时间:
        $row['prechenkintime'] = $time->format('Y-m-d H:i');
        $time = new \DateTime($row['date'] . " " . $row['end_time']);
        $interval = new \DateInterval('PT30M');
        $time->add($interval);
        //课后打卡时间
        $row['prechenkouttime'] = $time->format('Y-m-d H:i');
        $time = new \DateTime($row['date'] . " " . $row['end_time']);
        $interval = new \DateInterval('PT12H');
        $time->add($interval);
        //评价开始时间
        $row['evalstart'] = $row['date'] . " " . $row['end_time'];
        //评价结束时间
        $row['evalend'] = $time->format('Y-m-d H:i');
        $row['ocourse_count'] = 0;
        //老师岗 $row['teacher_id']
        //管理岗 $row['manager_id']

        //班级
        $classes = model("Classes")->get($row['classes_id']);
        //班级名称 $classes["name"];
        //课程名称等于事件的名称 $classes["name"];
        $row["event_name"] = $classes["name"];
        //课程封面也就是等于事件的封面 $classes["name"];
        $row["ocourse_logo"] = $classes["cover"];
        $row["event_content"] = trim(strip_tags($classes["intro"]));

        //教学点设置
        $ocation = model("Ocation")->where("id", "=", $row["ocation_id"])->select();
        //教室 $ocation["name"]
        //上课地址 $row["address"]
        $addresslv_id_B = model("Ocation")->get($ocation[0]["pid"]);
        //二级教学点地址
        if (is_array($row['addresslv_id_B'])) {
            $row['addresslv_id_B'] = $addresslv_id_B["name"];
        }
        if (is_array($row['addresslv_id_B'])) {
            $addresslv_id_A = model("Ocation")->get($addresslv_id_B["pid"]);
            //一级教学点地址
            $row['addresslv_id_A'] = $addresslv_id_A["name"];
        }

        //一周几次课
        $row['classes_per_week'] = $ocation[0]["classes_per_week"];

        //学科设置
        $subjectlv = model("Subjectlv")->where("id", "=", $row["subject_lv"])->select();
        $row["subjectlv"] = $subjectlv[0]["name"];
        $subjectlv_id_B = model("Subjectlv")->get($subjectlv[0]["pid"]);
        //二级学科
        $row['addresslv_id_B'] = $subjectlv_id_B["name"];
        //一级学科
        $subjectlv_id_A = model("Subjectlv")->get($subjectlv_id_B["pid"]);
        if ($subjectlv_id_A == null) {
            $row['subjectlv_id_A'] = $row["subjectlv"];
            $row['subjectlv_id_B'] = "";
        } elseif ($subjectlv_id_A["pid"] == 0) {
            $row['subjectlv_id_A'] = $subjectlv_id_B["name"];
            $row['subjectlv_id_B'] = $subjectlv[0]["name"];
        } else {
            $row['subjectlv_id_A'] = $subjectlv_id_A["name"];
        }

        //课程设置
        $classplant = model("Classplant")->where("table_id", "=", $coursetable['id'])->where("classes_id", "=", $row["classes_id"])->select();
        if (array_key_exists("0", $classplant)) {
            //课程长度:
            $row['ocourse_count'] = $classplant[0]["ocourse_count"];
            $row['ocation_type'] = $classplant[0]["ocation_type"];
            $row['suject_lv'] = model("Subjectlv")->getSubject_lvTextAttr($classplant[0]["subject_lv"]);
        }
        //周几 $classplant['week'];
        //时段 上午 中午 下午 晚上 $classplant['daytime'];
        //当天第几节课 $classplant['oindex'];
        //开始上课时间 $classplant['start_time'];
        //下课时间 $classplant['end_time'];
        //起始日期 $classplant['start_date'];
        //结束日期 $classplant['end_date'];
        //是否排除国际假日 $classplant['exclint_holidays'];
        //是否同一间教室 $classplant['same_classroom'];
        //是否同一教学点 $classplant['same_ocation'];
        //能否排在同一天 $classplant['can_same_day'];
        //时段选择 $classplant['daytime'];


        $this->assignconfig("admin", $_SESSION['think']['admin']);
        $this->view->assign("row", $row);
        $this->view->assign("table_id_name", $table_id_name);
        $this->view->assign("rel_type", $_SESSION['think']['admin']['rel_type'] ?? '');
        $this->view->assign("rel_id", $_SESSION['think']['admin']['rel_id'] ?? '');

        $teacherIds = !empty($row['teacher_id']) ? explode(",", $row['teacher_id']) : [];
        $managerIds = !empty($row['manager_id']) ? explode(",", $row['manager_id']) : [];
        $data = array("args" => [[], $teacherIds, $managerIds]);

        $leftOptions = $this->generateOptions('/user/user/lists', $data);
        $leftOptions = ($leftOptions == null) ? [] : $leftOptions["leftOptions"];
        $rightOptions = $this->generateOptions('/user/user/lists');
        $rightOptions = ($rightOptions == null) ? [] : $rightOptions["list"];
        $this->assignconfig("leftOptions", $leftOptions);
        $this->assignconfig("rightOptions", $rightOptions);
        return $this->view->fetch();

    }

    /**
     * 添加
     */
    public
    function view($ids = NULL)
    {
        $row = $this->model->getRow($ids);
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    public
    function lists($ids = NULL)
    {
        $where = "where 1=1";
        if (($_SESSION['think']['admin']['group_id'] ?? 0) != 1) {
            $where .= " and city_id='" . ($_SESSION['think']['admin']['city_id'] ?? '') . "'";
        }
        $page = 1;
        $pagesize = 50;
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                $keyField = $this->request->request("keyField");
                $keyValue = $this->request->request("keyValue");
                $page = $this->request->request("pageNumber");
                $page = ($page == null) ? 1 : $page;
                $pagesize = $this->request->request("pageSize");
                $pagesize = ($pagesize == null) ? 10 : $pagesize;
                if ($keyValue != "") {
                    $nn = preg_split("/\,/", $keyValue);
                    for ($i = 0; $i < count($nn); $i++) {
                        $where .= " and $keyField='{$nn[$i]}'";
                    }
                }
            } else {
                $where = "where status=1";
                if (isset($_REQUEST['city_id']) && $_REQUEST['city_id'] > 0) {
                    //$where.=" and city_id=".$_REQUEST['city_id'];
                }
            }
        }
        $sql = "select id,week name from dev002_gxyusheng.fa_course_table_autoplan $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
        $list = db()->query($sql);

//        $r_name = model("School")->field("id,name")->where("id", "=", $_REQUEST['school_id'])->select();
//        if (empty($list)&&!empty($r_name)) {
//            $data = array(
//                "type" => "file",
//                "pid" => 0,
//                "name" => $r_name[0]['name']."学科",
//                "title" => $r_name[0]['name'],
//                "ismenu" => 0,
//                "createtime" => time(),
//                "updatetime" => time(),
//                "status" => 1,
//                "school_id" => $_REQUEST['school_id'],
//            );
//            $this->model->create($data);
//        }
//        $sql="select id,pid,name,title,status from dev002_gxyusheng.fa_course_table_autoplan $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
//        $list = db()->query($sql);
//        $tree = Tree::instance();
//        $tree->init($list, 'pid');
//        $list = $tree->getTreeList($tree->getTreeArray(0), 'name');

        $total = db()->query("select count(1) as c from  dev002_gxyusheng.fa_course_table_autoplan $where")[0];
        echo json_encode(array("list" => $list, "total" => $total['c']));
        exit;
    }

    public
    function status($ids = '')
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }
        try {
            $result = $this->model->where("id", "=", $ids)->field("status")->find();
            $v = $result->status == 1 ? 0 : 1;
            $this->model->where("id", "=", $ids)->update(["status" => $v]);
            $this->success('切换成功!');
        } catch (Exception $e) {
            $this->error('切换失败!' . $e->getMessage());
        }
    }

    public
    function changeStatus($ids = '')
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }
        try {
            $result = $this->model->where("id", "=", $ids)->field("status")->find();
            $v = $result->status == 1 ? 0 : 1;
            $this->model->where("id", "=", $ids)->update(["status" => $v]);
            $this->success('切换成功!');
        } catch (Exception $e) {
            $this->error('切换失败!' . $e->getMessage());
        }
    }

    public function export($ids)
    {
        // 获取数据
        $list = model("Autoplantemp")->where("id", "in", $ids)->select();

        // 设置导出文件名
        $filename = '课程计划详情表' . date('YmdHis');

        // 设置表头
        $headers = [
            'Id',
            '项目名称',
            '班名',
            '教室',
            '课程',
            '老师',
            '时段',
            '日期',
            '时间',
            '周几',
            '课节',
            '备注',
        ];

        // 设置数据
        $data = [];
        foreach ($list as $item) {
            $ocation = model("Ocation")->get($item['ocation_id']);
            $courseable = model("Coursetable")->get($item['table_id']);
            $classes = model("Classes")->get($item['classes_id']);
            $data[] = [
                $item['id'],
                $courseable['name'],
                $classes['name'],
                $ocation['name'],
                model("Subjectlv")->getSubjectlv_idTextAttr($item['subject_lv']),
                model("User")->getUser_idTextAttr($item['teacher_id']),
                $item['daytime'],
                $item['date'] . " ",
                $item['start_time'] . " - " . $item['end_time'],
                $item['week'],
                $item['class_index'] . "/" . $item['count_total'],
                $item['bz'],
            ];
        }

        // 调用导出方法
        $this->exportExcel($filename, $headers, $data);
    }

    protected function exportExcel($filename, $headers, $data)
    {
        // 创建一个新的Spreadsheet对象
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // 设置工作表标题
        $sheet->setTitle("课程安排");

        // 添加标题，并合并A1到K1单元格，设置居中
        $title = '课程计划详情';
        $sheet->mergeCells('A1:K1');
        $sheet->setCellValue('A1', $title);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A1')->getFont()->setBold(true);

        // 设置表头
        $sheet->fromArray($headers, null, 'A2');
        // 设置数据
        $sheet->fromArray($data, null, 'A3');

        // 应用边框样式
        $styleArray = [
            'borders' => [
                'outline' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THICK,
                    'color' => ['argb' => '000000'],
                ],
                'inside' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '000000'],
                ],
            ],
        ];

        $lastRow = $sheet->getHighestRow();
        $lastColumn = $sheet->getHighestColumn();
        $sheet->getStyle('A1:' . $lastColumn . $lastRow)->applyFromArray($styleArray);

        // 设置表头颜色为#ccc
        $sheet->getStyle('A2:' . $lastColumn . '2')->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setARGB('FFCCCCCC');  // #ccc in ARGB format

        // 启用筛选功能
        $sheet->setAutoFilter('A2:' . $lastColumn . '2');

        // 设置列宽为内容自适应
        foreach (range('A', $sheet->getHighestColumn()) as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        // 设置响应头
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
        header('Cache-Control: max-age=0');

        // 输出文件
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }


    function del($ids = null)
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }
        try {
            model("Autoplantemp")->where("id", "in", $ids)->delete();
            $this->success('删除成功!');
        } catch (Exception $e) {
            $this->error('删除失败!' . $e->getMessage());
        }
    }

    function redo()
    {
        $table_id = $this->request->param('table_id');
        try {
            $result = Db::name('fa_course_table_autoplan_temp')
                ->where('table_type',1)
                ->where('table_id', $table_id)->delete();
            $this->success('已经清空全部旧排课信息，正在重新生成排课信息!');
        } catch (Exception $e) {
            $this->success('已经清空全部旧排课信息，正在重新生成排课信息!');
        }
    }


}
