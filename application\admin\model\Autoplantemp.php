<?php

namespace app\admin\model;

use think\Model;
use fast\Tree;

class Autoplantemp extends Model
{
    // 表名
    public $name = 'course_table_autoplan_temp';
    public $prefix_new = 'fa_';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    public $data;


    public
    function getClasses_idTextAttr($value, $data = array())
    {
        $s = array();
        $content = [];
        $nn = preg_split("/,/", $value);
        for ($i = 0; $i < count($nn); $i++) {
            if ($nn[$i] == "") continue;
            if (isset($content[$nn[$i]])) $s[] = $content[$nn[$i]];
        }
        return join(",", $s);
    }

    public
    function getOcation_idTextAttr($value, $data = array())
    {
        $s = array();
        $content = [];
        $nn = preg_split("/,/", $value);
        for ($i = 0; $i < count($nn); $i++) {
            if ($nn[$i] == "") continue;
            if (isset($content[$nn[$i]])) $s[] = $content[$nn[$i]];
        }
        return join(",", $s);
    }

    public function getAutoplantemp_idTextAttr($value, $data=[])
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("Autoplantemp")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

}