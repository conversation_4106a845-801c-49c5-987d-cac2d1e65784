<?php
// 示例数据
$data = [
    [
        "name" => "203教室",
        "schedule_data" => '[{"daytime":"上午","oindex":"1","start_time":"08:00","end_time":"09:40"},{"daytime":"上午","oindex":"2","start_time":"09:55","end_time":"11:25"}]',
        "disable_week_range" => "",
        "disable_week_time" => ""
    ],
    [
        "name" => "204教室",
        "schedule_data" => '[{"daytime":"上午","oindex":"1","start_time":"08:30","end_time":"10:00"},{"daytime":"上午","oindex":"2","start_time":"10:15","end_time":"11:45"}]',
        "disable_week_range" => "周二 下午,周三 下午",
        "disable_week_time" => "周二 1节,周二 2节,周三 1节,周三 2节"
    ],
];

$weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
$daytimes = ['上午', '中午', '下午', '晚上'];

function parseDisableRanges($disable_week_range, $disable_week_time) {
    $disabled_ranges = [];
    $disabled_times = [];

    if (!empty($disable_week_range)) {
        $ranges = explode(',', $disable_week_range);
        foreach ($ranges as $range) {
            list($day, $time) = explode(' ', trim($range));
            $disabled_ranges[] = [$day, $time];
        }
    }

    if (!empty($disable_week_time)) {
        $times = explode(',', $disable_week_time);
        foreach ($times as $time) {
            list($day, $section) = explode(' ', trim($time));
            $disabled_times[] = [$day, intval(str_replace('节', '', $section))];
        }
    }

    return [$disabled_ranges, $disabled_times];
}

echo "<style>
    table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
    th, td { border: 1px solid #000; padding: 5px; text-align: center; min-width: 100px; height: 30px; }
    th { background-color: #f2f2f2; }
</style>";

foreach ($data as $room) {
    echo "<h3>{$room['name']}</h3>";
    echo "<table>";

    // 表头
    echo "<tr><th>时间段</th>";
    foreach ($weekdays as $day) {
        echo "<th>$day</th>";
    }
    echo "</tr>";

    list($disabled_ranges, $disabled_times) = parseDisableRanges($room['disable_week_range'], $room['disable_week_time']);
    $schedule = json_decode($room['schedule_data'], true);

    foreach ($daytimes as $time_name) {
        $classes_in_time = array_filter($schedule, function($class) use ($time_name) { return $class['daytime'] === $time_name; });

        if (count($classes_in_time) > 0) {
            foreach ($classes_in_time as $class) {
                echo "<tr><td>第{$class['oindex']}节 {$time_name}</td>";
                foreach ($weekdays as $day) {
                    $is_disabled_range = false;
                    $is_disabled_time = false;

                    foreach ($disabled_ranges as $range) {
                        if ($range[0] === $day && $range[1] === $time_name) {
                            $is_disabled_range = true;
                            break;
                        }
                    }

                    foreach ($disabled_times as $time) {
                        if ($time[0] === $day && $time[1] == $class['oindex']) {
                            $is_disabled_time = true;
                            break;
                        }
                    }

                    $style = '';
                    if ($is_disabled_range || $is_disabled_time) {
                        $style = 'background-color: ' . ($is_disabled_range ? '#ccc' : '#eee') . ';';
                    }

                    $cell_content = ($class['daytime'] === $time_name && in_array($day, array_map(function($item) { return $item['day']; }, $schedule))) ? "{$class['start_time']}-{$class['end_time']}" : "&nbsp;";

                    if ($cell_content === "&nbsp;" && ($is_disabled_range || $is_disabled_time)) {
                        $cell_content = ''; // 如果被禁用且没有课程，则保持单元格内容为空白
                    }

                    echo "<td style='$style'>$cell_content</td>";
                }
                echo "</tr>";
            }
        } else {
            echo "<tr><td>{$time_name}</td>";
            foreach ($weekdays as $day) {
                $is_disabled_range = false;
                foreach ($disabled_ranges as $range) {
                    if ($range[0] === $day && $range[1] === $time_name) {
                        $is_disabled_range = true;
                        break;
                    }
                }
                $style = $is_disabled_range ? 'background-color: #ccc;' : '';
                echo "<td style='$style'>&nbsp;</td>";
            }
            echo "</tr>";
        }
    }

    echo "</table>";
}
?>