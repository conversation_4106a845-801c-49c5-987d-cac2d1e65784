<?php

namespace app\admin\validate;

use think\Validate;

class Menu extends Validate
{

    /**
     * 验证规则
     */
    protected $rule = [
        'icon' => 'require|regex:.{8,40}|unique:admin,icon',
    ];

    protected function checkDistrictId($value, $rule, $data)
    {
        if ($data['city_id']== '0') {
            return true;
        }
        if($value==""||$value=="-1"){
            return false;
        }
        return true;
    }

    /**
     * 提示消息
     */
    protected $message = [];

    /**
     * 字段描述
     */
    protected $field = [];

    /**
     * 验证场景
     */
    protected $scene = [
        'add' => [
            'icon' => 'require',
            'city_id' => 'require',
            'district_id' => 'checkDistrictId',
            'name' => 'require|regex:.{8,40}|unique:school,name',
            'address' => 'require',
        ],
        'edit' => [
            'icon' => 'require',
            'city_id' => 'require',
            'district_id' => 'checkDistrictId',
            'name' => 'require|regex:.{8,40}',
            //'address' => 'require',
        ]
    ];

    public function __construct(array $rules = [], $message = [], $field = [])
    {
        $this->field = [
            'icon' => "__firstfieldname__",
        ];
        $this->message = array_merge($this->message, [
            'icon.regex' => '学校名称长度出错',
            'icon.require' => '省份必选',
        ]);
        parent::__construct($rules, $message, $field);
    }

}
