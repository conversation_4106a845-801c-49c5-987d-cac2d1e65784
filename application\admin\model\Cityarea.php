<?php

namespace app\admin\model;
use think\Model;
use fast\Tree;

class Cityarea extends Model
{
    // 表名
    public $name = 'cityarea';
    public $prefix_new = 'fa_';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    public $data;

    public function getProvince_idTextAttr($value)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("cityarea")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }
    public function getCity_idTextAttr($value)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("cityarea")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    public function getDistrict_idTextAttr($value)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("cityarea")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    protected function setCity_idAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

}