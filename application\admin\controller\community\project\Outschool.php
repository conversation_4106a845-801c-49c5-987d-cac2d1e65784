<?php

namespace app\admin\controller\community\project;

use app\common\controller\Backend;
use think\Db;

/**
 * 校外课堂方案管理
 *
 * @icon fa fa-graduation-cap
 */
class Outschool extends Backend
{
    /**
     * Outschool模型对象
     * @var \app\admin\model\community\project\Outschool
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\community\project\Outschool;
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $user=$this->auth->getUserInfo();
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->where('community_id', '=', $user['community_id'])
                ->order($sort, $order)
                ->paginate($limit);

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        $user=$this->auth->getUserInfo();
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                // 如果是社区管理员，自动设置社区ID
                if ($this->auth->group_id == 8) {
                    $params['community_id'] = $user['community_id'];
                }

                // 设置学校名称
                $params['school_name'] = $this->auth->school_name;

                // 格式化时间字段
                if (isset($params['start_time'])) {
                    if (is_string($params['start_time'])) {
                        // 如果是字符串，转换为时间戳
                        $params['start_time'] = strtotime($params['start_time']);
                    } elseif (is_numeric($params['start_time'])) {
                        // 如果是数字，保持原样
                        $params['start_time'] = intval($params['start_time']);
                    }
                }
                if (isset($params['end_time'])) {
                    if (is_string($params['end_time'])) {
                        // 如果是字符串，转换为时间戳
                        $params['end_time'] = strtotime($params['end_time']);
                    } elseif (is_numeric($params['end_time'])) {
                        // 如果是数字，保持原样
                        $params['end_time'] = intval($params['end_time']);
                    }
                }


                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        // 传递学校名称到视图
        $this->view->assign('school_name', $this->auth->school_name);
        return $this->view->fetch();
    }

    function edit($ids = null)
    {
        $user=$this->auth->getUserInfo();
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                // 如果是社区管理员，自动设置社区ID
                if ($this->auth->group_id == 8) {
                    $params['community_id'] = $user['community_id'];
                }

                // 设置学校名称
                $params['school_name'] = $this->auth->school_name;

                // 格式化时间字段
                if (isset($params['start_time'])) {
                    if (is_string($params['start_time'])) {
                        // 如果是字符串，转换为时间戳
                        $params['start_time'] = strtotime($params['start_time']);
                    } elseif (is_numeric($params['start_time'])) {
                        // 如果是数字，保持原样
                        $params['start_time'] = intval($params['start_time']);
                    }
                }
                if (isset($params['end_time'])) {
                    if (is_string($params['end_time'])) {
                        // 如果是字符串，转换为时间戳
                        $params['end_time'] = strtotime($params['end_time']);
                    } elseif (is_numeric($params['end_time'])) {
                        // 如果是数字，保持原样
                        $params['end_time'] = intval($params['end_time']);
                    }
                }
                //入库
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model
                        ->allowField(true)
                        ->save($params, ['id' => $ids]);
                    Db::commit();
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error(__('No rows were updated'));
                    }
                } catch (ValidateException $e) {

                }
            }
        }else{
            $row = $this->model->get($ids);
            if (!$row) {
                $this->error(__('No Results were found'));
            }
            $this->view->assign("row", $row);
            $this->view->assign('school_name', $this->auth->school_name);
            return $this->view->fetch();
        }
    }
}