<div class="panel panel-default panel-intro">
    <!-- 功能板块操作提示栏 -->
    <div class="school-guide-panel" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
        <div class="panel-header" style="padding: 15px 20px; border-bottom: 1px solid rgba(255,255,255,0.2); display: flex; justify-content: space-between; align-items: center;">
            <h4 style="margin: 0; font-weight: 600;"><i class="fa fa-tags"></i> 学校项目管理模块操作指引</h4>
            <button class="btn btn-sm" id="toggleGuide" style="background: rgba(255,255,255,0.2); border: none; color: white; border-radius: 4px; padding: 5px 10px;">
                <i class="fa fa-chevron-up"></i> 收起
            </button>
        </div>
        <div class="guide-content" id="guideContent" style="padding: 20px;">
            <!-- 当前步骤信息 -->
            <div class="current-step-info" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <span class="step-badge" style="background: #28a745; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; margin-right: 10px;">当前步骤</span>
                    <h5 style="margin: 0; font-weight: 600;">第1步：基础信息录入</h5>
                </div>
                <p style="margin: 0; opacity: 0.9;">请在此页面完成教学点标签设置，为教学点分类管理做准备。</p>
            </div>
            
            <!-- 进度条 -->
            <div class="progress-container" style="margin-bottom: 20px;">
                <div class="progress" style="height: 8px; background: rgba(255,255,255,0.2); border-radius: 4px; overflow: hidden;">
                    <div class="progress-bar" style="width: 33.33%; background: #28a745; height: 100%; transition: width 0.3s ease;"></div>
                </div>
                <div style="display: flex; justify-content: space-between; margin-top: 8px; font-size: 12px; opacity: 0.8;">
                    <span>步骤 4/12</span>
                    <span>33.33% 完成</span>
                </div>
            </div>
            
            <!-- 操作步骤列表 -->
            <div class="steps-list" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                <div class="step-item active" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <span class="step-number" style="background: #28a745; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; margin-right: 10px;">1.4</span>
                        <strong>教学点标签管理</strong>
                        <span class="current-badge" style="background: #ffc107; color: #000; padding: 2px 8px; border-radius: 10px; font-size: 10px; margin-left: 10px;">当前</span>
                    </div>
                    <p style="margin: 0 0 10px 34px; font-size: 13px; opacity: 0.9;">完成教学点标签设置</p>
                    <button class="btn btn-sm btn-success" onclick="window.top.location.href='/subjectlv/subjectlv?ref=addtabs'" style="margin-left: 34px; padding: 4px 12px; font-size: 12px;">如果已完善，点击到下一步</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
    $(document).ready(function() {
        // 收起/展开功能
        $('#toggleGuide').click(function() {
            var content = $('#guideContent');
            var icon = $(this).find('i');
            if (content.is(':visible')) {
                content.slideUp();
                icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                $(this).html('<i class="fa fa-chevron-down"></i> 展开');
            } else {
                content.slideDown();
                icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
                $(this).html('<i class="fa fa-chevron-up"></i> 收起');
            }
        });
    });
    </script>
    
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh,add,delete')}
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('addresslv/addresslv/edit')}"
                           data-operate-del="{:$auth->check('addresslv/addresslv/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
