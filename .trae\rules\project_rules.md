# 项目开发规范与要求

## 一、错误处理规范
当出现类似 `Uncaught TypeError: Cannot read properties of undefined (reading 'document') at HTMLButtonElement.<anonymous> (fast.js?v=1745485774:233:196)` 的错误时，需立即定位到指定文件（如 `fast.js`）的对应行号（如 233 行），对代码进行详细检查与错误排查。

## 二、数据库建表规范

### 1. 表名规范
- 所有表名统一添加前缀 `fa_`，以此增强表名的辨识度和系统性。

### 2. 字段注释规范
- 每个字段都必须添加 `COMMENT` 注释，注释格式为：中文字段名 + `[参数]`，参数之间使用逗号分隔，可根据实际需求添加多个参数。
- **参数说明**：
  - `index`：表明该字段适用于列表页的列表项展示，内容不宜过长，以保证列表显示的简洁性。
  - `key`：代表此字段为关键字段，在数据检索、关联等操作中具有重要作用。
  - `hidden`：表示该字段为隐藏字段，在前端展示或常规操作中不直接显示。
  - `type`：用于指定字段类型。若为时间类型，必须明确设定为 `type:datetime`。常见的 `type` 选项包括 `varchar`、`text`、`int`、`radio`、`select`、`select3`、`selects`、`checkbox`、`date`、`datetime` 等。其中，`text` 类型适用于长文本内容，即可以包含回车换行符的文本。
  - `content`：针对 `select`、`radio`、`checkbox` 类型的字段，用于定义其可选参数项。格式为 `1=男|2=女`，若存在常用选项（如民族、性别等）或上下文中已有的选项，应优先使用这些选项进行补充，尽可能全面地列出。

### 3. 通用字段要求
- 每张数据表都必须包含以下字段：
  - `create_time`：记录数据创建时间。
  - `update_time`：记录数据更新时间。
  - `delete_time`：记录数据逻辑删除时间，若采用逻辑删除机制。
  - `status`：数据状态字段，默认 `0` 表示失效，`1` 表示正常。
  - `weigh`：权重字段，可用于排序等场景。

### 4. 表名注释
- 为每张表添加 `COMMENT` 注释，注释内容应为清晰易懂的中文名，以便直观地了解表的用途。

### 5. 模型文件创建
- 需按照 FastAdmin 标准格式创建模型文件，在模型文件中，模型名称、方法名称均不需要添加额外前缀，保持命名的简洁性和规范性。
- 在碰到上传组件时，请按下面的代码格式创建模型类似的组件：
            <div class="form-group">
                <label for="c-cover" class="control-label col-xs-12 col-sm-2">班级封面:</label>
                <div class="col-xs-12 col-sm-8" id="div-cover">
                    <div class="form-inline">
                        <input id="c-cover" class="form-control" size="38" name="row[cover]" type="text"
                               value="{$row.cover}" data-tip="">
                        <span><button type="button" id="plupload-cover" class="btn btn-danger plupload"
                                      data-input-id="c-cover" data-mimetype="image/*" data-multiple="false"
                                      data-preview-id="p-cover"><i
                                class="fa fa-upload"></i> {:__('Upload')}</button></span>
                        <span><button type="button" id="fachoose-cover" class="btn btn-primary fachoose"
                                      data-input-id="c-cover" data-mimetype="image/*" data-multiple="false"><i
                                class="fa fa-list"></i> {:__('Choose')}</button></span>
                        <ul class="row list-inline plupload-preview" id="p-cover"></ul>
                    </div>
                </div>
            </div>

## 三、FastAdmin 框架开发规范

### 1. Vue 内容及 API 地址分析

#### 请求方式判断与字段查找
- 查找可能属于字段的 `name` 名称。若存在 `textarea` 文本框，请求方式采用 `post`；对于简单的数据请求，使用 `get` 请求。请求的 `url` 地址统一以 `https://ys.yangjc.com/ysapi/v1/` 开头，并添加参数 `XDEBUG_SESSION_START=54354`。

#### GET 请求处理
- 代码功能判断：深入分析当前 Vue 代码，准确判断其属于列表页的 `getList` 功能还是内容页的 `getInfo` 功能。
- 控制器代码生成：以当前文件的上层目录和文件名为类名，在 `app\api\controller\v1\` 目录下生成控制器代码。通过关联 `classes` 班级表和 `class_student` 班级学生对应表，获取班级的 `name` 和 `id` 信息。同时，在控制器文件中设置 `protected $noNeedLogin = '*'; protected $noNeedRight = '*';`，以确定访问权限。
- 路由代码生成：生成对应的路由代码，格式为 `Route::rule('{{当前文件夹名}}/{{当前文件名}}','app\api\controller\v1\控制器名@方法名', 'GET');`，确保请求能够正确映射到相应的控制器和方法。
- 页面代码修改：对当前页面代码进行优化，实现通过远程加载数据的功能。对于列表页，实现列表循环展示数据条目；对于内容页，实现数据读取并替换原有空白或静态写死的内容，最终提供更新后的当前文件。

#### POST 请求处理
- 在 `POST` 请求场景下，统一以 `form-data` 方式组织 `data`，确保数据传输的正确性和兼容性。

### 2. 额外输出要求

#### 标题输出
- 提取表单的名称或其要实现的主要功能作为标题，并结合文件名进行输出。若页面代码中存在 `.items - center.justify - center` 相关代码，应从中提取关键信息作为标题的补充。

#### 控制器 PHP 代码输出
- 输出符合 FastAdmin 框架规范的控制器 PHP 代码，命名空间统一为 `namespace app\api\controller\v1;`，并使用默认的模型路径 `use app\common\model;`。

#### 前端脚本代码输出
- 输出完整的前端脚本代码，在脚本代码中，API 地址应去掉 `XDEBUG_SESSION_START` 参数，以保证在正式环境中的正常访问。

#### 路由代码生成与输出
- 生成通用的路由代码，格式为 `Route::rule('{{当前文件夹名}}/{{当前文件名}}','app\api\controller\v1\控制器名@方法名', 'GET/POST');`，以适应不同请求方式的需求，并进行输出展示。

#### cUrl 命令生成与输出
- 根据开发需求，生成用于申请和推送数据的 cUrl 命令，在命令中，访问路径应使用父级文件夹名和文件名，确保命令的准确性和可执行性，并将生成的 cUrl 命令进行输出。