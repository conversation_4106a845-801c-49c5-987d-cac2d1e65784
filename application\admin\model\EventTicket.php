<?php

namespace app\admin\model;

use think\Model;

class EventTicket extends Model
{
    // 表名
    protected $name = 'event_ticket';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = 'delete_time';

    // 追加属性
    protected $append = [
        'ticket_type_text',
        'status_text'
    ];

    public function getTicketTypeList()
    {
        return ['1' => '普通券', '2' => '特殊券'];
    }

    public function getStatusList()
    {
        return ['0' => '未使用', '1' => '已使用'];
    }

    public function getTicketTypeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['ticket_type'];
        $list = $this->getTicketTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : $data['status'];
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


}