<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js"></script>
<style>
    /* 选项卡样式 */
    .nav-tabs {
        border-bottom: 1px solid #ddd;
        margin-bottom: 20px;
    }

    .nav-tabs > li {
        float: left;
        margin-bottom: -1px;
    }

    .nav-tabs > li > a {
        margin-right: 2px;
        line-height: 1.42857143;
        border: 1px solid transparent;
        border-radius: 4px 4px 0 0;
        padding: 10px 15px;
    }

    .nav-tabs > li > a:hover {
        border-color: #eee #eee #ddd;
    }

    .nav-tabs > li.active > a,
    .nav-tabs > li.active > a:hover,
    .nav-tabs > li.active > a:focus {
        color: #555;
        cursor: default;
        background-color: #fff;
        border: 1px solid #ddd;
        border-bottom-color: transparent;
    }

    .tab-content {
        padding: 20px;
        border: 1px solid #ddd;
        border-top: none;
        background-color: #fff;
        margin-top: -20px;
    }
    table.table-bordered tr td {
        height: 35px;
        line-height: 35px;
        padding: 0 5px;
    }
</style>
<script>
$(document).ready(function() {
    // 初始化选项卡
    $('.nav-tabs a').click(function(e) {
        e.preventDefault();
        $(this).tab('show');
    });
});

</script>

<form id="edit-form" class="form-horizontal" style="position:relative;background: #fff" role="form"
      data-toggle="validator" method="POST" action="">

    <div class="panel-body">
        <!-- 选项卡导航 -->
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active">
                <a href="#basic-info" aria-controls="basic-info" role="tab" data-toggle="tab">基本信息</a>
            </li>
            <li role="presentation">
                <a href="#project-detail" aria-controls="project-detail" role="tab" data-toggle="tab">项目详情</a>
            </li>
            <li role="presentation">
                <a href="#class-info" aria-controls="class-info" role="tab" data-toggle="tab">班级情况</a>
            </li>
            <li role="presentation">
                <a href="#student-detail" aria-controls="student-detail" role="tab" data-toggle="tab" id="student-detail-tab">报名学员详情</a>
            </li>
            <li role="presentation">
                <a href="#registration-info" aria-controls="registration-info" role="tab" data-toggle="tab">报名情况</a>
            </li>
            <li role="presentation">
                <a href="#registration-summary" aria-controls="registration-summary" role="tab" data-toggle="tab">报名情况汇总</a>
            </li>
            <li role="presentation">
                <a href="#pre-registration-map" aria-controls="pre-registration-map" role="tab" data-toggle="tab">预报名地理位置分布图</a>
            </li>
        </ul>



        <!-- 选项卡内容 -->
        <div class="tab-content">
            <!-- 基本信息 -->
            <div role="tabpanel" class="tab-pane fade in active" id="basic-info">
                <!-- 原有所有表单内容 -->
                <input type="hidden" id="row" name="data" value='<?php echo json_encode($row);?>'/>
                <input type="hidden" id="id" name="row[id]" value='{$row.id}'/>
                <div class="form-group">
                    <label for="c-school_id" class="control-label col-xs-12 col-sm-2">学校:</label>
                    <div class="col-xs-12 col-sm-8" id="div-school_id">
                        <select name="row[school_id]" id="c-school_id" class="form-control">
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-apply_type" class="control-label col-xs-12 col-sm-2">报名类型:</label>
                    <div class="col-xs-12 col-sm-8" id="div-apply_type">
                        <label for="row[apply_type]-0">
                            <input id="row[apply_type]-0" name="row[apply_type]" type="radio" value="0" {if $row["apply_type"]=="0"}checked{/if}
                            /> 预报名
                        </label>
                        <label for="row[apply_type]-1">
                            <input id="row[apply_type]-1" name="row[apply_type]" type="radio" value="1" {if $row["apply_type"]=="1"}checked{/if}
                            /> 报名缴费
                        </label>
                    </div>
                </div>


                <div class="form-group">
                    <label for="c-table_id" class="control-label col-xs-12 col-sm-2">课程表:</label>
                    <div class="col-xs-12 col-sm-8" id="div-table_id">
                        <select name="row[table_id]" id="c-table_id" class="form-control">
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-title" class="control-label col-xs-12 col-sm-2">报名项目名称:</label>
                    <div class="col-xs-12 col-sm-8" id="div-title">
                        <input type="text" id="c-title" name="row[title]" value="{$row.title}" class="form-control"
                               title="title" placeholder="" data-rule="" data-tip="报名项目名称"/>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-intro" class="control-label col-xs-12 col-sm-2">课程介绍 :</label>
                    <div class="col-xs-12 col-sm-8" id="div-intro">
                        <textarea name="row[intro]" id="c-intro" class="form-control editor" data-rule="" rows="5"
                                  data-tip="课程介绍 ">{$row.intro}</textarea>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-cover" class="control-label col-xs-12 col-sm-2">封面 :</label>
                    <div class="col-xs-12 col-sm-8" id="div-cover">
                        <div class="form-inline">
                            <input id="c-cover" class="form-control" size="37" name="row[cover]" type="text"
                                   value="{$row.cover}" data-tip="">
                            <span><button type="button" id="plupload-cover" class="btn btn-danger plupload"
                                          data-input-id="c-cover" data-mimetype="image/*" data-multiple="false"
                                          data-preview-id="p-cover"><i
                                    class="fa fa-upload"></i> {:__('Upload')}</button></span>
                            <span><button type="button" id="fachoose-cover" class="btn btn-primary fachoose"
                                          data-input-id="c-cover" data-mimetype="image/*" data-multiple="false"><i
                                    class="fa fa-list"></i> {:__('Choose')}</button></span>
                            <ul class="row list-inline plupload-preview" id="p-cover"></ul>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-amount" class="control-label col-xs-12 col-sm-2">学费 :</label>
                    <div class="col-xs-12 col-sm-8" id="div-amount">
                        <div class="form-inline">
                            <input id="c-amount" class="form-control" size="37" name="row[amount]" type="text"
                                   value="{$row.amount}" data-tip="">
                            <ul class="row list-inline plupload-preview" id="p-amount"></ul>
                        </div>
                    </div>
                </div>


                <div class="form-group">
                    <label for="c-start_time" class="control-label col-xs-12 col-sm-2">开始时间:</label>
                    <div class="col-xs-12 col-sm-8" id="div-start_time">
                        <input type="text" id="c-start_time" name="row[start_time]" value="{$row.start_time}"
                               class="form-control datetimepicker" data-use-current="true"
                               data-date-format="YYYY-MM-DD HH:mm:ss" data-tip="开始时间" data-rule=""/>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-end_time" class="control-label col-xs-12 col-sm-2">结束时间 :</label>
                    <div class="col-xs-12 col-sm-8" id="div-end_time">
                        <input type="text" id="c-end_time" name="row[end_time]" value="{$row.end_time}"
                               class="form-control datetimepicker" data-use-current="true"
                               data-date-format="YYYY-MM-DD HH:mm:ss" data-tip="结束时间 " data-rule=""/>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-max_num" class="control-label col-xs-12 col-sm-2">招生人数限制:</label>
                    <div class="col-xs-12 col-sm-8" id="div-max_num">
                        <input type="number" id="c-max_num" name="row[max_num]" value="0" class="form-control"
                               title="max_num" data-rule="integer" placeholder="只能填入整数" data-tip="招生人数"/>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-is_real" class="control-label col-xs-12 col-sm-2">是否正式报名:</label>
                    <div class="col-xs-12 col-sm-8" id="div-is_real">
                        <label for="row[is_real]-0"><input id="row[is_real]-0" name="row[is_real]" type="radio"
                                                           value="0" {if $row["is_real"]=="0"}checked{/if} /> 否</label>

                        <label for="row[is_real]-1"><input id="row[is_real]-1" name="row[is_real]" type="radio"
                                                           value="1" {if $row["is_real"]=="1"}checked{/if} /> 是</label>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-status" class="control-label col-xs-12 col-sm-2">状态:</label>
                    <div class="col-xs-12 col-sm-8" id="div-status">
                        <div class="btn-group" data-toggle="buttons">
                            <label class="btn btn-default {if $row["status"]=="0"}active{/if}">
                            <input type="radio" name="row[status]" value="0" {if $row["status"]=="0"}checked{/if}> 未开启
                            </label>
                            <label class="btn btn-default {if $row["status"]=="1"}active{/if}">
                            <input type="radio" name="row[status]" value="1" {if $row["status"]=="1"}checked{/if}> 已开启
                            </label>
                            <label class="btn btn-default {if $row["status"]=="2"}active{/if}">
                            <input type="radio" name="row[status]" value="2" {if $row["status"]=="2"}checked{/if}> 已撤回
                            </label>
                        </div>
                    </div>
                </div>


                <div class="form-group">
                    <label for="c-is_close" class="control-label col-xs-12 col-sm-2">是否关闭:</label>
                    <div class="col-xs-12 col-sm-8" id="div-is_close">
                        <label for="row[is_close]-0"><input id="row[is_close]-0" name="row[is_close]" type="radio"
                                                            value="0" {if $row["is_close"]=="0"}checked{/if} />
                            未关闭</label>

                        <label for="row[is_close]-1"><input id="row[is_close]-1" name="row[is_close]" type="radio"
                                                            value="1" {if $row["is_close"]=="1"}checked{/if} />
                            关闭</label>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-is_opened" class="control-label col-xs-12 col-sm-2">是否开启过:</label>
                    <div class="col-xs-12 col-sm-8" id="div-is_opened">
                        <label for="row[is_opened]-0"><input id="row[is_opened]-0" name="row[is_opened]" type="radio"
                                                             value="0" {if $row["is_opened"]=="0"}checked{/if} /> 未开启</label>

                        <label for="row[is_opened]-1"><input id="row[is_opened]-1" name="row[is_opened]" type="radio"
                                                             value="1" {if $row["is_opened"]=="1"}checked{/if} /> 已开启</label>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-is_finished" class="control-label col-xs-12 col-sm-2">是否结束 :</label>
                    <div class="col-xs-12 col-sm-8" id="div-is_finished">
                        <label for="row[is_finished]-0"><input id="row[is_finished]-0" name="row[is_finished]"
                                                               type="radio" value="0" {if $row["is_finished"]=="0"}checked{/if}
                            /> 未结束</label>

                        <label for="row[is_finished]-1"><input id="row[is_finished]-1" name="row[is_finished]"
                                                               type="radio" value="1" {if $row["is_finished"]=="1"}checked{/if}
                            /> 已结束</label>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-is_user_end" class="control-label col-xs-12 col-sm-2">是否手动截止:</label>
                    <div class="col-xs-12 col-sm-8" id="div-is_user_end">
                        <label for="row[is_user_end]-0"><input id="row[is_user_end]-0" name="row[is_user_end]"
                                                               type="radio" value="0" {if $row["is_user_end"]=="0"}checked{/if}
                            /> 否</label>

                        <label for="row[is_user_end]-1"><input id="row[is_user_end]-1" name="row[is_user_end]"
                                                               type="radio" value="1" {if $row["is_user_end"]=="1"}checked{/if}
                            /> 是</label>
                    </div>
                </div>

                <!--是否允许积分兑换-->
                <div class="form-group">
                    <label for="c-is_use_integral" class="control-label col-xs-12 col-sm-2">是否允许积分兑换:</label>
                    <div class="col-xs-12 col-sm-8" id="div-is_use_integral">
                        <label for="row[is_use_integral]-0"><input id="row[is_use_integral]-0" name="row[is_use_integral]"
                                                               type="radio" value="0" {if $row["is_use_integral"]=="0"}checked{/if}
                            /> 否</label>
                        <label for="row[is_use_integral]-1"><input id="row[is_use_integral]-1" name="row[is_use_integral]"
                                                               type="radio" value="1" {if $row["is_use_integral"]=="1"}checked{/if}
                            /> 是</label>
                    </div>
                </div>


                <div class="form-group">
                    <label for="c-is_end" class="control-label col-xs-12 col-sm-2">是否截止:</label>
                    <div class="col-xs-12 col-sm-8" id="div-is_end">
                        <label for="row[is_end]-0"><input id="row[is_end]-0" name="row[is_end]" type="radio" value="0"
                                                          {if $row["is_end"]=="0"}checked{/if} /> 未截止</label>

                        <label for="row[is_end]-1"><input id="row[is_end]-1" name="row[is_end]" type="radio" value="1"
                                                          {if $row["is_end"]=="1"}checked{/if} /> 已截止</label>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-dead_time" class="control-label col-xs-12 col-sm-2">截止时间 :</label>
                    <div class="col-xs-12 col-sm-8" id="div-dead_time">
                        <input type="text" id="c-dead_time" name="row[dead_time]" value="{$row.dead_time}"
                               class="form-control datetimepicker" data-use-current="true"
                               data-date-format="YYYY-MM-DD HH:mm:ss" data-tip="截止时间 " data-rule=""/>
                    </div>
                </div>
                <!-- ... 原有所有表单内容 ... -->
                <div class="form-group layer-footer">
                    <label class="control-label col-xs-12 col-sm-2"></label>
                    <div class="col-xs-12 col-sm-8">
                        <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
                        <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                    </div>
                </div>
            </div>

            <!-- 项目详情 -->
            <div role="tabpanel" class="tab-pane fade" id="project-detail">
                <!-- 项目详情内容 -->
                <div id="info-container">
                    <div class="info-item">
                        事项名称: <span id="item-name-display"></span>
                    </div>
                    <div class="info-item">
                        ID: <span id="item-id-display"></span>
                    </div>
                    <div class="info-item">
                        说明: <span id="description-display"></span>
                    </div>
                    <div class="info-item">
                        预报名开始时间: <span id="pre-start-time-display"></span>
                    </div>
                    <div class="info-item">
                        预报名结束时间: <span id="pre-end-time-display"></span>
                    </div>
                    <div class="info-item">
                        预报名数量限制: <span id="pre-registration-limit-display"></span>
                    </div>
                </div>
            </div>

            <!-- 班级情况 -->
            <div role="tabpanel" class="tab-pane fade" id="class-info">
                <!-- 班级情况内容 -->
                <div id="class-list"></div>
            </div>

            <!-- 报名学员详情 -->
            <div role="tabpanel" class="tab-pane fade" id="student-detail">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">报名学员列表</h3>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>账号名称</th>
                                        <th>手机号</th>
                                        <th>性别</th>
                                        <th>报名课程</th>
                                        <th>报名时间</th>
                                        <th>应缴金额</th>
                                        <th>缴费情况</th>
                                        <th>缴费时间</th>
                                        <th>缴费订单号</th>
                                    </tr>
                                </thead>
                                <tbody id="student-list">
                                    <tr>
                                        <td colspan="8" class="text-center">点击上方标签加载数据...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 报名情况 -->
            <div role="tabpanel" class="tab-pane fade" id="registration-info">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">报名情况列表</h3>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>账号名称</th>
                                        <th>报名课程</th>
                                        <th>报名时间</th>
                                        <th>应缴金额</th>
                                        <th>缴费情况</th>
                                        <th>缴费时间</th>
                                        <th>缴费订单号</th>
                                    </tr>
                                </thead>
                                <tbody id="registration-list">
                                    <tr>
                                        <td colspan="8" class="text-center">点击上方标签加载数据...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 报名情况汇总 -->
            <div role="tabpanel" class="tab-pane fade" id="registration-summary">
                <!-- 报名情况汇总内容 -->
            </div>


        </div>
    </div>
</form>

<script src="/assets/js/backend/applyform/student.js"></script>
