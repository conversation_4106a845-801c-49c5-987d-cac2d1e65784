<?php
namespace app\admin\model;

use think\Model;

class CreditDetails extends Model
{
    protected $table = 'fa_credit_details';

    // 设置当前模型自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = 'delete_time';

    // 是否需要自动完成
    protected $insert = [];
    protected $update = [];

    // 验证规则
    protected $rule = [
        'credit_id' => 'require|integer',
        'detail' => 'require',
        'status' => 'require|in:有效,已失效',
        'create_time' => 'datetime',
        'update_time' => 'datetime',
        'delete_time' => 'datetime',
        'weigh' => 'require|integer',
        'description' => 'max:255',
    ];

    // 错误信息
    protected $message = [
        'credit_id.require' => '关联学分不能为空',
        'credit_id.integer' => '关联学分必须是整数',
        'detail.require' => '详细信息不能为空',
        'status.require' => '状态不能为空',
        'status.in' => '状态必须是有效或已失效',
        'create_time.datetime' => '创建时间格式不正确',
        'update_time.datetime' => '更新时间格式不正确',
        'delete_time.datetime' => '删除时间格式不正确',
        'weigh.require' => '权重不能为空',
        'weigh.integer' => '权重必须是整数',
        'description.max' => '描述不能超过255个字符',
    ];

    // 定义关联关系
    public function credits()
    {
        return $this->belongsTo(Credits::class, 'credit_id', 'id');
    }
}