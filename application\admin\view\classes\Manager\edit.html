<form id="edit-form" class="form-horizontal" style="position:relative;background: #fff" role="form" data-toggle="validator" method="POST" action="">
    
    <div class="panel-body">
        <div id="myTabContent" class="tab-content" >
            <div class="tab-pane fade active in" id="tabs_0">
                <input type="hidden" id="row" name="data" value='<?php echo json_encode($row);?>' />
                <div class="form-group">
                        <label for="c-class_id" class="control-label col-xs-12 col-sm-2">班级:</label>
                        <div class="col-xs-12 col-sm-8" id="div-class_id" >
                            <input type="number" id="c-class_id" name="row[class_id]" value="0" class="form-control" title="class_id" data-rule="integer" placeholder="只能填入整数"  data-tip="班级"  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-teacher_id" class="control-label col-xs-12 col-sm-2">老师:</label>
                        <div class="col-xs-12 col-sm-8" id="div-teacher_id" >
                            <input type="number" id="c-teacher_id" name="row[teacher_id]" value="0" class="form-control" title="teacher_id" data-rule="integer" placeholder="只能填入整数"  data-tip="老师"  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-add_time" class="control-label col-xs-12 col-sm-2">加入时间:</label>
                        <div class="col-xs-12 col-sm-8" id="div-add_time" >
                            <input type="text" id="c-add_time" name="row[add_time]" value="{$row.add_time}" class="form-control datetimepicker"  data-use-current="true"  data-date-format="YYYY-MM-DD HH:mm:ss" data-tip="加入时间" data-rule=""  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-status" class="control-label col-xs-12 col-sm-2">状态:</label>
                        <div class="col-xs-12 col-sm-8" id="div-status" >                    
                            <input id="c-status" name="row[status]" type="hidden" value="{$row.status}">
                            <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-status" data-yes="1" data-no="0">
                                <i class="fa fa-toggle-on text-success fa-flip-horizontal fa-2x"></i>
                            </a>
                            <div data-favisible="switch=1" class="p-3">已开户</div>                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-school_id" class="control-label col-xs-12 col-sm-2">学校:</label>
                        <div class="col-xs-12 col-sm-8" id="div-school_id" >
                            <input type="number" id="c-school_id" name="row[school_id]" value="0" class="form-control" title="school_id" data-rule="integer" placeholder="只能填入整数"  data-tip="学校"  />
                        </div>
                   </div>
                <div class="form-group layer-footer">
                    <label class="control-label col-xs-12 col-sm-2"></label>
                    <div class="col-xs-12 col-sm-8">
                        <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
                        <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                    </div>
                </div>
            </div>
            
        </div>
    </div>
</form>

