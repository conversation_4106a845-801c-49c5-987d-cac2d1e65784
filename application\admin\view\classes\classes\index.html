<div class="panel panel-default panel-intro">
    <script src="/assets/js/echarts.min.js"></script>
    <link rel="stylesheet" href="/assets/css/layer.min.css">
    <script src="/assets/js/layer.min.js"></script>
    
    <!-- 功能板块操作提示栏 -->
    <div class="school-guide-panel" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
        <div class="panel-header" style="padding: 15px 20px; border-bottom: 1px solid rgba(255,255,255,0.2); display: flex; justify-content: space-between; align-items: center;">
            <h4 style="margin: 0; font-weight: 600;"><i class="fa fa-users"></i> 学校项目管理模块操作指引</h4>
            <button class="btn btn-sm" id="toggleGuide" style="background: rgba(255,255,255,0.2); border: none; color: white; border-radius: 4px; padding: 5px 10px;">
                <i class="fa fa-chevron-up"></i> 收起
            </button>
        </div>
        <div class="guide-content" id="guideContent" style="padding: 20px;">
            <!-- 当前步骤信息 -->
            <div class="current-step-info" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <span class="step-badge" style="background: #28a745; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; margin-right: 10px;">当前步骤</span>
                    <h5 style="margin: 0; font-weight: 600;">第1步：基础信息录入</h5>
                </div>
                <p style="margin: 0; opacity: 0.9;">请在此页面完成学校当前所有班级的信息录入，包括班级名称、年级、人数等信息。</p>
            </div>
            
            <!-- 进度条 -->
            <div class="progress-container" style="margin-bottom: 20px;">
                <div class="progress" style="height: 8px; background: rgba(255,255,255,0.2); border-radius: 4px; overflow: hidden;">
                    <div class="progress-bar" style="width: 25%; background: #28a745; height: 100%; transition: width 0.3s ease;"></div>
                </div>
                <div style="display: flex; justify-content: space-between; margin-top: 8px; font-size: 12px; opacity: 0.8;">
                    <span>步骤 3/12</span>
                    <span>25% 完成</span>
                </div>
            </div>
            
            <!-- 操作步骤列表 -->
            <div class="steps-list" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                <div class="step-item active" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <span class="step-number" style="background: #28a745; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; margin-right: 10px;">1.3</span>
                        <strong>班级信息录入</strong>
                        <span class="current-badge" style="background: #ffc107; color: #000; padding: 2px 8px; border-radius: 10px; font-size: 10px; margin-left: 10px;">当前</span>
                    </div>
                    <p style="margin: 0 0 10px 34px; font-size: 13px; opacity: 0.9;">在班级管理模块里完成学校当前所有班级的信息录入</p>
                    <button class="btn btn-sm btn-success" onclick="window.top.location.href='/addresslv/addresslv?relType=cityarea&ref=addtabs'" style="margin-left: 34px; padding: 4px 12px; font-size: 12px;">如果已完善，点击到下一步</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
    $(document).ready(function() {
        // 收起/展开功能
        $('#toggleGuide').click(function() {
            var content = $('#guideContent');
            var icon = $(this).find('i');
            if (content.is(':visible')) {
                content.slideUp();
                icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                $(this).html('<i class="fa fa-chevron-down"></i> 展开');
            } else {
                content.slideDown();
                icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
                $(this).html('<i class="fa fa-chevron-up"></i> 收起');
            }
        });
    });
    </script>
    
    {:build_heading()}

    <div class="panel-body">
    <!-- 主选项卡：学校班级/社区班级 -->
    <ul class="nav nav-tabs" id="mainClassTab">
        <li class="active"><a href="#schoolTab" data-toggle="tab" data-reltype="1">学校班级</a></li>
        <li><a href="#communityTab" data-toggle="tab" data-reltype="2">社区班级</a></li>
    </ul>
    <div class="tab-content" style="margin-top:15px;">
        <div class="tab-pane fade in active" id="schoolTab">
            <!-- 二级tab -->
            <ul class="nav nav-pills" id="schoolSubTab">
                <li class="active"><a href="#schoolOngoing" data-toggle="pill" data-classstatus="1">正在进行中</a></li>
                <li><a href="#schoolEnded" data-toggle="pill" data-classstatus="0">已经结束</a></li>
            </ul>
            <div class="tab-content" style="margin-top:10px;">
                <div class="tab-pane fade in active" id="schoolOngoing">
                    <!-- 表格区域 -->
                    <div id="schoolOngoingTableContainer"></div>
                </div>
                <div class="tab-pane fade" id="schoolEnded">
                    <div id="schoolEndedTableContainer"></div>
                </div>
            </div>
        </div>
        <div class="tab-pane fade" id="communityTab">
            <ul class="nav nav-pills" id="communitySubTab">
                <li class="active"><a href="#communityOngoing" data-toggle="pill" data-classstatus="1">正在进行中</a></li>
                <li><a href="#communityEnded" data-toggle="pill" data-classstatus="0">已经结束</a></li>
            </ul>
            <div class="tab-content" style="margin-top:10px;">
                <div class="tab-pane fade in active" id="communityOngoing">
                    <div id="communityOngoingTableContainer"></div>
                </div>
                <div class="tab-pane fade" id="communityEnded">
                    <div id="communityEndedTableContainer"></div>
                </div>
            </div>
        </div>
    </div>

            <!-- <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('classes/classes/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('classes/classes/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('classes/classes/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>

                        <div class="dropdown btn-group {:$auth->check('class/class/multi')?'':'hide'}">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> {:__('More')}</a>
                            <ul class="dropdown-menu text-left" role="menu">
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=normal"><i class="fa fa-eye"></i> {:__('Set to normal')}</a></li>
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=hidden"><i class="fa fa-eye-slash"></i> {:__('Set to hidden')}</a></li>
                            </ul>
                        </div>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover"
                           data-operate-edit="{:$auth->check('classes/classes/edit')}"
                           data-operate-del="{:$auth->check('classes/classes/del')}"
                           width="100%">
                    </table>
                </div>
            </div> -->

        </div>
    </div>
</div>
