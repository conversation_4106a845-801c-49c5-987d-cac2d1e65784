<?php

namespace app\admin\model;
use think\Model;
use fast\Tree;

class student extends Model
{
    // 表名
    public $name = 'class_student';
    public $prefix_new = 'eb_';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    //protected $createTime = 'create_time';
    protected $updateTime = false;
    public $data;


    public function getApply_statueTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "1"=>"已缴费",
            "0"=>"未缴费",
            );
        $nn=preg_split("/,/",$value);
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

}