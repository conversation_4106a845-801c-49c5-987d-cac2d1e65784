<?php

namespace app\admin\model;
use think\Model;
use fast\Tree;

class Slider extends Model
{
    // 表名
    public $name = 'slider';
    public $prefix_new = 'fa_';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    public $data;


    public function getTypeTextAttr($value, $data)
    {
        $s=array();
        $content=array(
            "1"=>"右到左滑动",
            "2"=>"上到下滑动",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

}