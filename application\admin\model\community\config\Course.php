<?php

namespace app\admin\model\community\config;

use think\Model;

class Course extends Model
{
    // 表名
    protected $name = 'community_course_config';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'status_text'
    ];
    
    public function getStatusList()
    {
        return ['0' => __('Status 0'), '1' => __('Status 1')];
    }
    
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }
    
    // 关联社区表
    public function community()
    {
        return $this->belongsTo('app\admin\model\Community', 'community_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    
    // 关联公益项目表
    public function publicwelfare()
    {
        return $this->belongsTo('app\admin\model\community\Publicwelfare', 'publicwelfare_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
