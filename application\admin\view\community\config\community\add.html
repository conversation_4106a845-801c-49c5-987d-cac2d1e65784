<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-code" data-rule="required" class="form-control" name="row[code]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-name" data-rule="required" class="form-control selectpicker" name="row[name]">
                {foreach name="communityCodeList" item="vo"}
                <option value="{$vo}">{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    {if !$simplifiedView}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Community_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-community_id" data-rule="required" class="form-control selectpicker" name="row[community_id]">
                {foreach name="communityList" item="vo" key="k"}
                <option value="{$k}" {if $k==$community_id}selected{/if}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" class="form-control" name="row[remark]" rows="5"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['1'=>__('Status 1'), '0'=>__('Status 0')])}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weigh" class="form-control" name="row[weigh]" type="number" value="0">
        </div>
    </div>
    {else}
    <!-- 隐藏字段，用于在简化视图中传递参数 -->
    <input type="hidden" name="row[community_id]" value="{$community_id}">
    <input type="hidden" name="row[status]" value="1">
    <input type="hidden" name="row[weigh]" value="0">
    {/if}
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
