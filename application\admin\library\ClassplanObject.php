<?php

namespace app\admin\library;

class ClassplanObject
{
    private $classplanId;
    private $classes_id;
    private $tableId;
    private $allClassesPlans;
    public  $classesPlans;
    public  $classroomId;
    public  $classesPlan;
    public  $classesWeekMaxCount;
    public $ocationIds;


    function setTableId($tableId){
        $this->tableId=$tableId;
        $this->buildClassRoomData();
    }

    private function buildClassRoomData(){
        $data = array();
        $subQuery = model("Classplant")
            ->alias("p")
            ->field("p.*")
            ->where("p.status", 1)
            ->order("p.id ASC") // 这里可以根据实际需求修改排序规则
            ->group("classes_id")
            ->buildSql();
        $r = collection(model("Classroomeplan")
            ->alias("c")
            ->join([$subQuery => 'p'], "p.classes_id=c.classes_id","right")
            ->field("c.*,p.classes_per_week")
            ->where("p.table_id", $this->tableId)
            ->where("p.status", 1)
            ->select())->toArray();
        $this->ocationIds = array();
        $inarr=[];
        for ($i = 0; $i < count($r); $i++) {
            $data[$r[$i]["classes_id"]][] = [
                'id' => $r[$i]["id"],
                'classes_id' => $r[$i]["classes_id"],
                'ocation_id' => $r[$i]["ocation_id"],
                'week' => $r[$i]["week"],
                'daytime' => $r[$i]["daytime"],
                'classes_per_week'=>$r[$i]["classes_per_week"],
            ];
            if(!in_array($r[$i]["ocation_id"],$inarr)){
                $this->ocationIds[$r[$i]["classes_id"]][]=$r[$i]["ocation_id"];
                $inarr[$r[$i]["classes_id"]][]=$r[$i]["ocation_id"];
            }
        }
        $this->ocationIds=$this->ocationIds;
        foreach ($data as $key => $value) {
            $count=count($data[$key]);
            for($j=0;$j<$count;$j++){
                $data[$key][$j]["planCount"]=$count;
            }
        }

        $this->allClassesPlans=$data;
    }

    public function getClassPlansByClassId($class_id){
        $allClassesPlansdata=$this->allClassesPlans;
        $this->classes_id=$class_id;
        if($this->classesWeekMaxCount==0||!isset($this->classesWeekMaxCount)){
            $r = collection(model("Classplant")
                ->field("classes_per_week")
                ->where("table_id", $this->tableId)
                ->where("classes_id", $class_id)
                ->where("status", 1)
                ->select())->toArray();
            $this->classesWeekMaxCount=$r[0]["classes_per_week"];
        }
        if(array_key_exists($class_id,$allClassesPlansdata)) {
            $this->classesPlans = $allClassesPlansdata[$class_id];
        }else{
            $this->classesPlans=array();
        }
        return $this->classesPlans;
    }

    public function getClassesId(){
        return $this->classes_id;
    }

    public function getClassesPlan(){
        return $this->classesPlan;
    }

    public function getClassesPlans(){
        return $this->classesPlans;
    }


    public function getPlanByPlanId($planId){
        for ($i = 0; $i < count($this->classesPlans); $i++) {
            if ($this->classesPlans[$i]["id"] == $planId) {
                $plan=$this->classesPlans[$i];
                break;
            }
        }
        $this->classesPlan=$plan;
        $this->classplanId=$plan["id"];
        return $plan;
    }

    private function getWeekMaxCount(){
        $max=0;
        $r=model("Classplant")->where("classes_id",$this->classes_id)->where("status",1)->select();
        $this->classesWeekMaxCount=$r[0]->classes_per_week;
        return $r[0]['classes_per_week'];
    }

    private function getplanWeekCount(){
        return count($this->classesPlans);
    }

    public function getClassesLeaveWeekCount(){
        return $this->classesWeekMaxCount-count($this->classesPlans);
    }



    public function getplansByWeek($week){
        $plans=array();
        for ($i = 0; $i < count($this->data); $i++) {
            if ($this->plandata[$i]["week"] == $week) {
                $plans[]=$this->plandata[$i];
            }
        }
        return $plans;
    }

    public function getplanByWeekAndDaytime($week,$daytime){
        $plan=array();
        if(!empty($this->classesPlans)&&!empty($this->classesPlans)) {
            for ($i = 0; $i < count($this->classesPlans); $i++) {
                if (isset($this->classesPlans[$i])&&!empty($this->classesPlans[$i])&& $this->classesPlans[$i]["week"] == $week && $this->classesPlans[$i]["daytime"] == $daytime) {
                    $plan = $this->classesPlans[$i];
                }
            }
        }
        return $plan;
    }

    public function deleteplanById($planId){
        $plan=array();
        for ($i = 0; $i < count($this->plandata); $i++) {
            if ($this->plandata[$i]["id"] == $planId) {
                unset($this->plandata[$i]);
            }
        }
    }

    function checkOcationConflict($classes_id,$ocation_id){
        if(isset($this->ocationIds[$classes_id])&&in_array($ocation_id,$this->ocationIds[$classes_id])){
            return true;
        }
        return false;
    }
    
    // 添加缺失的 hasOcationId 方法
    function hasOcationId($classes_id, $ocation_id){
        if(isset($this->ocationIds[$classes_id])&&in_array($ocation_id,$this->ocationIds[$classes_id])){
            return true;
        }
        return false;
    }

}