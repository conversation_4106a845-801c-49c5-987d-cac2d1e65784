<?php

namespace app\admin\model;
use think\Db;
use think\Model;
use fast\Tree;

class Ocation extends Model
{
    // 表名
    public $name = 'ocation';
    public $prefix_new = 'fa_';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = false;
    protected $falseDelete =false;
    public $data;


    public function getAddress_lvTextAttr($value, $data)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("addresslv")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    public function getOcation_idTextAttr($value, $data=[])
    {
        if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $data=json_decode(json_encode($data),true);
            if(empty($data)||array_key_exists('table_type', $data) && $data['table_type']=='1'||array_key_exists('classes_type', $data) && $data['classes_type']=='1'){
                $r = Db::name("Ocation")->find(array("id" => $nn[$i]));
            }else{
                $r = Db::name("community_code_config")->find(array("id" => $nn[$i]));
            }
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    public function getAddress_lv_octionlevel_TextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "1"=>"一级教学点",
            "2"=>"二级教学点",
            "3"=>"三级教学点",
            "4"=>"上课教室",
        );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getOction_typeTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "1"=>"标准类",
            "2"=>"书画类",
            "3"=>"肢体类",
            "4"=>"综合类",
        );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    protected function setAddress_lvAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getRel_typeTextAttr($value, $data)
    {
        $s=array();
        $content=array(
            "1"=>"学校",
            "2"=>"社区",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }


    public function getOcation_typeTextAttr($value, $data=array())
    {
        if($value=="") return "标准";
        $s=array();
        $content=array(
            "1"=>"标准",
            "2"=>"书画",
            "3"=>"肢体",
            "4"=>"综合",
        );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getRel_idTextAttr($value, $data)
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

}