<?php

namespace app\admin\library;

use fast\Tree;

class ClassesObject
{
    private $tableId;
    public $classRoomData;
    public $classroomAvailability;
    public $ocationIds;
    function setTableId($tableId)
    {
        $this->tableId = $tableId;
        $this->classroomAvailability = $this->getClassroomAvailability();
    }

    function getTableId()
    {
        return $this->tableId;
    }

    private function getClassroomAvailability(){
        $r = model("Coursetable")->where("status", "=", 1)->where("id", "=", $this->tableId)->select();
        $coursetable_name = $r[0]["name"];//课程项目名称
        $classeseids = explode(",", $r[0]["classes_id"]);
        $r = model("Ocation")->where("status", "=", 1);
        $r = $r->select();
        $list = collection($r)->toArray();
        $tree = Tree::instance();
        $tree->init($list, 'pid');
        $list = $tree->getTreeList($tree->getTreeArray(365), 'name');
        $data = array();
        for ($i = 0; $i < count($list); $i++) {
            $v = $list[$i];
            $data[] = [
                'id' => $v['id'],
                'parent' => $v['pid'] ? $v['pid'] : '#',
                'name' => $v['name'],
                'address_lv_octionlevel' => $v['address_lv_octionlevel'],
                'classes_per_week' => $v['classes_per_week'],
                'ocation_type' => $v['ocation_type'],
                'weekdays' => $v['weekdays'],
                'daytime' => $v['daytime'],
                'disable_week_range' => $v['disable_week_range'],
                'disable_week_time' => $v['disable_week_time'],
                'schedule_data' => $v['schedule_data'],
                'starttime' => $v['starttime'],
                'endtime' => $v['endtime'],
                'type' => $v['type'],
                'status' => $v['status'],
            ];
        }
        $r=$data;
        $data = [];
        for ($i = 0; $i < count($r); $i++) {
            if($r[$i]["address_lv_octionlevel"]<3) continue;
            $row = $r[$i];
            $row["name"]=preg_replace("/&nbsp;|│|├|└/", "", $row["name"]);
            $data[$row["name"]] = array();
            $data[$row["name"]]["addresslv_name"] = model("Ocation")->getOcation_idTextAttr($row["parent"]);
            $data[$row["name"]]["classroom_id"] = $row["id"];
            $data[$row["name"]]["type"] = model("Ocation")->getOcation_typeTextAttr($row["ocation_type"]);
            $data[$row["name"]]["daytimeoindex"] = !empty($row["schedule_data"]) ? json_decode($row["schedule_data"]) : null;
            if ($data[$row["name"]]["daytimeoindex"] == null) {
                $data[$row["name"]]["daytimeoindex"] = array(
                    "0" => array(
                        "daytime" => "上午",
                        "oindex" => "1",
                        "start_time" => "08:30",
                        "end_time" => "10:00",
                    ),
                    "1" => array(
                        "daytime" => "上午",
                        "oindex" => "2",
                        "start_time" => "10:10",
                        "end_time" => "11:30",
                    ),
                    "2" => array(
                        "daytime" => "下午",
                        "oindex" => "3",
                        "start_time" => "13:30",
                        "end_time" => "15:00",
                    ),
                    "3" => array(
                        "daytime" => "下午",
                        "oindex" => "4",
                        "start_time" => "15:10",
                        "end_time" => "16:40",
                    )
                );
            }
            $data[$row["name"]]["max_lessons_per_week"] = $row["classes_per_week"];
            if ($data[$row["name"]]["max_lessons_per_week"] == 0) {
                $data[$row["name"]]["max_lessons_per_week"] = 1000;
            }
            $data[$row["name"]]["availability"] = weekdaysdaytime($row["weekdays"], $row["daytime"]);
            $data[$row["name"]]["disabled_times"] = week_daytime($row["disable_week_range"]);
            $data[$row["name"]]["disabled_slots"] = week_oindex($row["disable_week_time"]);
            $data[$row["name"]]["time_slots"] = time_slots($row["schedule_data"]);
            $data[$row["name"]]["start"] = substr($row["starttime"], 0, 10);
            $data[$row["name"]]["end"] = substr($row["endtime"], 0, 10);
        }
        return $data;
    }
}

