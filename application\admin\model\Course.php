<?php

namespace app\admin\model;
use think\Model;
use fast\Tree;

class Course extends Model
{
    // 表名
    public $name = 'course';
    public $prefix_new = 'eb_';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    public $data;

    public function insertUpdate($data, $uniqueColumns, $isUpdate = true)
    {
        // 构建查询条件
        $where = [];
        foreach ($uniqueColumns as $column) {
            if (isset($data[$column])) {
                $where[$column] = $data[$column];
            } else {
                // 可以根据实际情况处理错误，这里简单抛出异常
                throw new \Exception("Unique column $column not found in data");
            }
        }

        // 先查询是否存在
        $record = $this->where($where)->find();
        if ($record) {
            // 存在则更新
            $record->save($data);
            $insertId = $record->id;
        } else {
            // 不存在则插入
            $this->save($data);
            $insertId = $this->id;
        }
        return $insertId;
    }


    public function getCourse_typeTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "1"=>"学校课堂",
            "2"=>"社区课堂",
            "3"=>"校外课堂",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getIs_postTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "1"=>"是",
            "2"=>"否",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getSchool_idTextAttr($value)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("course")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['title'];
        }
        return join(",",$s);
    }

    public function getCourse_idTextAttr($value, $data=array())
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("Courset")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['title'];
        }
        return join(",",$s);
    }

    protected function setSchool_idAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getSqstrTextAttr($value)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("area")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['title'];
        }
        return join(",",$s);
    }

    protected function setSqstrAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getClass_typeTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "1"=>"声乐",
            "2"=>"美书",
            "3"=>"书法",
            "4"=>"舞蹈",
            "5"=>"健身",
            "6"=>"养生",
            "7"=>"诗词",
            "8"=>"烹饪",
            "9"=>"花艺",
            "10"=>"心理",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getSubject_lvTextAttr($value)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("subjectlv")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['title'];
        }
        return join(",",$s);
    }

    protected function setSubject_lvAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getOcation_idTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getAddress_lvTextAttr($value)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("course")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['title'];
        }
        return join(",",$s);
    }

    protected function setAddress_lvAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getCkrqsxtj_date_beginTextAttr($value, $data)
    {
        $value = $value ? $value : $data['ckrqsxtj_date_begin'];
        return is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    protected function setCkrqsxtj_date_beginAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    public function getSkip_holidayTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "0"=>"否",
            "1"=>"是",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getTeacherstrTextAttr($value)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("course")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['title'];
        }
        return join(",",$s);
    }

    protected function setTeacherstrAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getTeacher_idTextAttr($value)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("course")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['title'];
        }
        return join(",",$s);
    }

    protected function setTeacher_idAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getManager_idTextAttr($value)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("course")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['title'];
        }
        return join(",",$s);
    }

    protected function setManager_idAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getCheck_timeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['check_time'];
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setCheck_timeAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function getCourse_endTextAttr($value, $data)
    {
        $value = $value ? $value : $data['course_end'];
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setCourse_endAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function getCourse_startTextAttr($value, $data)
    {
        $value = $value ? $value : $data['course_start'];
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setCourse_startAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function getAfter_timeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['after_time'];
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setAfter_timeAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function getEvaluation_timeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['evaluation_time'];
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setEvaluation_timeAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setReminder_userAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getReminder_userTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "1"=>"学员",
            "2"=>"教师",
            "3"=>"管理员",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getCommunity_idTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }



    public function getRel_typeTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getRel_idTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

}