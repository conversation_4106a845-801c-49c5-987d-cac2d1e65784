<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>学生管理</title>
  <link rel="stylesheet" href="/assets/css/fastadmin.css">
  <script src="/assets/js/jquery.min.js"></script>
  <script src="/assets/js/fastadmin.js"></script>
</head>
<body>
<div class="panel panel-default">
  <div class="panel-heading">
    <h3 class="panel-title">学生管理</h3>
    <div class="panel-options">
      <button class="btn btn-primary btn-sm" onclick="addStudent()">
        <i class="fa fa-plus"></i> 添加学生
      </button>
    </div>
  </div>
  <div class="panel-body">
    <form id="searchForm" class="form-inline">
      <div class="form-group">
        <input type="text" name="keyword" class="form-control" placeholder="学生姓名">
      </div>
      <button type="button" class="btn btn-default" onclick="search()">
        <i class="fa fa-search"></i> 搜索
      </button>
    </form>
    <table id="studentTable" class="table table-bordered table-hover">
      <thead>
      <tr>
        <th>ID</th>
        <th>姓名</th>
        <th>性别</th>
        <th>手机号</th>
        <th>班级</th>
        <th>操作</th>
      </tr>
      </thead>
      <tbody>
      <!-- 数据动态加载 -->
      </tbody>
    </table>
    <div id="pagination" class="text-right"></div>
  </div>
</div>

<script>
  // 页面加载时获取学生列表
  $(function () {
    loadStudents();
  });

  // 加载学生列表
  function loadStudents(page = 1) {
    $.ajax({
      url: '/admin/classes/students/getList',
      type: 'GET',
      data: {
        page: page,
        keyword: $('input[name="keyword"]').val()
      },
      success: function (res) {
        if (res.code === 200) {
          renderTable(res.data.list);
          renderPagination(res.data.total, page);
        } else {
          alert(res.msg);
        }
      }
    });
  }

  // 渲染表格数据
  function renderTable(data) {
    var html = '';
    data.forEach(function (item) {
      html += `<tr>
                <td>${item.id}</td>
                <td>${item.name}</td>
                <td>${item.gender === 1 ? '男' : '女'}</td>
                <td>${item.mobile}</td>
                <td>${item.class_name}</td>
                <td>
                    <button class="btn btn-xs btn-primary" onclick="editStudent(${item.id})">编辑</button>
                    <button class="btn btn-xs btn-danger" onclick="deleteStudent(${item.id})">删除</button>
                </td>
            </tr>`;
    });
    $('#studentTable tbody').html(html);
  }

  // 渲染分页
  function renderPagination(total, page) {
    var html = `<ul class="pagination">`;
    var totalPages = Math.ceil(total / 10); // 每页10条数据
    for (var i = 1; i <= totalPages; i++) {
      html += `<li class="${i === page ? 'active' : ''}">
                <a href="javascript:;" onclick="loadStudents(${i})">${i}</a>
            </li>`;
    }
    html += `</ul>`;
    $('#pagination').html(html);
  }

  // 搜索
  function search() {
    loadStudents(1);
  }

  // 添加学生
  function addStudent() {
    layer.open({
      type: 2,
      title: '添加学生',
      content: '/admin/classes/students/add',
      area: ['800px', '600px'],
      end: function () {
        loadStudents();
      }
    });
  }

  // 编辑学生
  function editStudent(id) {
    layer.open({
      type: 2,
      title: '编辑学生',
      content: '/admin/classes/students/edit?id=' + id,
      area: ['800px', '600px'],
      end: function () {
        loadStudents();
      }
    });
  }

  // 删除学生
  function deleteStudent(id) {
    layer.confirm('确定删除该学生吗？', function () {
      $.ajax({
        url: '/admin/classes/students/delete',
        type: 'POST',
        data: { id: id },
        success: function (res) {
          if (res.code === 200) {
            layer.msg('删除成功');
            loadStudents();
          } else {
            layer.msg(res.msg);
          }
        }
      });
    });
  }
</script>
</body>
</html>
