<?php

namespace app\admin\model;
use think\Model;
use fast\Tree;

class Classplant extends Model
{
    // 表名
    public $name = 'course_table_classplant';
    public $prefix_new = 'fa_';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    public $data;

     public function getClass_idTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getSubject_lvTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getTeacher_idTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getExclint_dayTextAttr($value, $data)
    {
        $value = $value ? $value : $data['exclint_day'];
        return is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    protected function setExclint_dayAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    public function getDisable_week_rangeTextAttr($value)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("classplant")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r[''];
        }
        return join(",",$s);
    }

    protected function setDisable_week_rangeAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getMust_week_rangeTextAttr($value)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("classplant")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r[''];
        }
        return join(",",$s);
    }

    protected function setMust_week_rangeAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

}