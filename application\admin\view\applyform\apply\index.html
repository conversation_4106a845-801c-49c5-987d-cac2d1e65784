<div class="panel panel-default panel-intro">
    <!-- 功能板块操作提示栏 -->
    <div class="operation-guide-panel" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
        <div class="guide-header" style="padding: 15px 20px; border-bottom: 1px solid rgba(255,255,255,0.2); display: flex; justify-content: space-between; align-items: center;">
            <h4 style="margin: 0; font-weight: 600;"><i class="fa fa-graduation-cap"></i> 学校项目管理操作指引</h4>
            <button class="btn btn-sm" id="toggleGuide" style="background: rgba(255,255,255,0.2); border: none; color: white; border-radius: 4px; padding: 5px 10px;">
                <i class="fa fa-chevron-up"></i> 收起
            </button>
        </div>
        <div class="guide-content" id="guideContent" style="padding: 20px;">
            <!-- 当前步骤导航条 -->
            <div class="current-step-nav" style="background: rgba(255,255,255,0.15); padding: 12px 16px; border-radius: 6px; margin-bottom: 20px; border-left: 4px solid #ffd700;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div>
                        <span style="font-size: 14px; opacity: 0.9;">当前步骤：</span>
                        <strong id="step" style="font-size: 16px; color: #ffd700;">第5步 - 项目信息统计</strong>
                    </div>
                    <div style="font-size: 12px; opacity: 0.8;">
                        进度：8/10
                    </div>
                </div>
            </div>
            
            <!-- 进度条 -->
            <div class="progress-container" style="margin-bottom: 25px;">
                <div class="progress" style="height: 8px; background: rgba(255,255,255,0.2); border-radius: 4px; overflow: hidden;">
                    <div class="progress-bar" style="width: 80%; background: linear-gradient(90deg, #ffd700, #ffed4e); height: 100%; transition: width 0.3s ease;"></div>
                </div>
                <div style="display: flex; justify-content: space-between; margin-top: 8px; font-size: 11px; opacity: 0.8;">
                    <span>开始</span>
                    <span>80% 完成</span>
                    <span>结束</span>
                </div>
            </div>
            
            <!-- 操作步骤列表 -->
            <div class="steps-container">
                <div class="row">
                    <div class="col-md-6">
                        <div class="step-item completed" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px; margin-bottom: 10px; border-left: 4px solid #00ff88;">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div>
                                    <h6 style="margin: 0 0 5px 0; color: #00ff88;"><i class="fa fa-check-circle"></i> 第1.1步：学校基础信息录入</h6>
                                    <small style="opacity: 0.9;">完善学校基本信息和联系方式</small>
                                </div>
                                <button class="btn btn-sm btn-success" onclick="window.open('/school/school', '_blank')" style="padding: 4px 8px; font-size: 11px;">
                                    已完成
                                </button>
                            </div>
                        </div>
                        
                        <div class="step-item completed" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px; margin-bottom: 10px; border-left: 4px solid #00ff88;">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div>
                                    <h6 style="margin: 0 0 5px 0; color: #00ff88;"><i class="fa fa-check-circle"></i> 第1.2步：教学点录入</h6>
                                    <small style="opacity: 0.9;">设置各个教学点的地址和信息</small>
                                </div>
                                <button class="btn btn-sm btn-success" onclick="window.open('/school/ocation', '_blank')" style="padding: 4px 8px; font-size: 11px;">
                                    已完成
                                </button>
                            </div>
                        </div>
                        
                        <div class="step-item completed" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px; margin-bottom: 10px; border-left: 4px solid #00ff88;">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div>
                                    <h6 style="margin: 0 0 5px 0; color: #00ff88;"><i class="fa fa-check-circle"></i> 第1.3步：班级信息录入</h6>
                                    <small style="opacity: 0.9;">创建和管理各个班级信息</small>
                                </div>
                                <button class="btn btn-sm btn-success" onclick="window.open('/school/classes', '_blank')" style="padding: 4px 8px; font-size: 11px;">
                                    已完成
                                </button>
                            </div>
                        </div>
                        
                        <div class="step-item completed" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px; margin-bottom: 10px; border-left: 4px solid #00ff88;">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div>
                                    <h6 style="margin: 0 0 5px 0; color: #00ff88;"><i class="fa fa-check-circle"></i> 第1.4步：标签设置</h6>
                                    <small style="opacity: 0.9;">设置地区标签和学科标签</small>
                                </div>
                                <button class="btn btn-sm btn-success" onclick="window.open('/school/addresslv', '_blank')" style="padding: 4px 8px; font-size: 11px;">
                                    已完成
                                </button>
                            </div>
                        </div>
                        
                        <div class="step-item completed" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px; margin-bottom: 10px; border-left: 4px solid #00ff88;">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div>
                                    <h6 style="margin: 0 0 5px 0; color: #00ff88;"><i class="fa fa-check-circle"></i> 第2.1步：课程/项目管理</h6>
                                    <small style="opacity: 0.9;">管理课程表和项目信息</small>
                                </div>
                                <button class="btn btn-sm btn-success" onclick="window.open('/course/coursetable', '_blank')" style="padding: 4px 8px; font-size: 11px;">
                                    已完成
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="step-item completed" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px; margin-bottom: 10px; border-left: 4px solid #00ff88;">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div>
                                    <h6 style="margin: 0 0 5px 0; color: #00ff88;"><i class="fa fa-check-circle"></i> 第2.2步：班级参数修改</h6>
                                    <small style="opacity: 0.9;">调整班级的各项参数设置</small>
                                </div>
                                <button class="btn btn-sm btn-success" onclick="window.open('/school/coursetable/classplant', '_blank')" style="padding: 4px 8px; font-size: 11px;">
                                    已完成
                                </button>
                            </div>
                        </div>
                        
                        <div class="step-item completed" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px; margin-bottom: 10px; border-left: 4px solid #00ff88;">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div>
                                    <h6 style="margin: 0 0 5px 0; color: #00ff88;"><i class="fa fa-check-circle"></i> 第2.3步：排课管理</h6>
                                    <small style="opacity: 0.9;">进行自动排课和课程安排</small>
                                </div>
                                <button class="btn btn-sm btn-success" onclick="window.open('/coursetable/autoplan', '_blank')" style="padding: 4px 8px; font-size: 11px;">
                                    已完成
                                </button>
                            </div>
                        </div>
                        
                        <div class="step-item current" style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 6px; margin-bottom: 10px; border-left: 4px solid #ffd700; box-shadow: 0 2px 8px rgba(255,215,0,0.3);">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div>
                                    <h6 style="margin: 0 0 5px 0; color: rgb(255, 215, 0);"><i class="fa fa-clock-o"></i> 第3步：报名项目管理</h6>
                                    <small style="opacity: 0.9;">管理学生报名和项目申请</small>
                                </div>
                                <button class="btn btn-sm btn-warning" onclick="window.top.open('/coursetable/coursetable?infostep=9&ref=addtabs', '_blank')" style="padding: 4px 8px; font-size: 11px;">
                                    如果已完善，点击到下一步
                                </button>
                            </div>
                        </div>
                        
                        <div class="step-item pending" style="background: rgba(255,255,255,0.05); padding: 15px; border-radius: 6px; margin-bottom: 10px; border-left: 4px solid #6c757d; opacity: 0.7;">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div>
                                    <h6 style="margin: 0 0 5px 0; color: #00ff88;"><i class="fa fa-circle-o"></i> 第4步：课程事件信息</h6>
                                    <small style="opacity: 0.7;">管理课程相关的事件和活动</small>
                                </div>
                                <button class="btn btn-sm  btn-warning" style="padding: 4px 8px; font-size: 11px;">
                                    待完成
                                </button>
                            </div>
                        </div>
                        
                        <div class="step-item pending" style="background: rgba(255,255,255,0.05); padding: 15px; border-radius: 6px; margin-bottom: 10px; border-left: 4px solid #6c757d; opacity: 0.7;">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div>
                                    <h6 style="margin: 0 0 5px 0; color: #00ff88;"><i class="fa fa-circle-o"></i> 第5步：项目信息统计</h6>
                                    <small style="opacity: 0.7;">查看和分析项目数据统计</small>
                                </div>
                                <button class="btn btn-sm  btn-warning" style="padding: 4px 8px; font-size: 11px; ">
                                    待完成
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 操作提示 -->
            <div class="operation-tips" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px; margin-top: 20px;">
                <h6 style="margin: 0 0 10px 0; color: #ffd700;"><i class="fa fa-lightbulb-o"></i> 当前步骤操作提示：</h6>
                <ul style="margin: 0; padding-left: 20px; font-size: 13px; line-height: 1.6;">
                    <li>管理学生的报名申请，审核报名信息</li>
                    <li>设置报名项目的状态和要求</li>
                    <li>处理报名表单和相关文档</li>
                    <li>跟踪报名进度和统计数据</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
    $(document).ready(function() {
        // 收起/展开功能
        $('#toggleGuide').click(function() {
            var content = $('#guideContent');
            var icon = $(this).find('i');
            
            if (content.is(':visible')) {
                content.slideUp(300);
                icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                $(this).html('<i class="fa fa-chevron-down"></i> 展开');
            } else {
                content.slideDown(300);
                icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
                $(this).html('<i class="fa fa-chevron-up"></i> 收起');
            }
        });
        
        // 响应式处理
        function handleResize() {
            if ($(window).width() < 768) {
                $('.steps-container .row').removeClass('row');
                $('.steps-container .col-md-6').removeClass('col-md-6').addClass('col-xs-12');
            } else {
                $('.steps-container > div').addClass('row');
                $('.steps-container .col-xs-12').removeClass('col-xs-12').addClass('col-md-6');
            }
        }
        
        handleResize();
        $(window).resize(handleResize);
        
        // 步骤按钮悬停效果
        $('.step-item button').hover(
            function() {
                $(this).css('transform', 'scale(1.05)');
            },
            function() {
                $(this).css('transform', 'scale(1)');
            }
        );
    });
    </script>
    
    <style>
    .operation-guide-panel {
        transition: all 0.3s ease;
    }
    
    .step-item {
        transition: all 0.3s ease;
    }
    
    .step-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    .step-item button {
        transition: all 0.2s ease;
        white-space: nowrap;
    }
    
    .step-item.current {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { box-shadow: 0 2px 8px rgba(255,215,0,0.3); }
        50% { box-shadow: 0 4px 16px rgba(255,215,0,0.5); }
        100% { box-shadow: 0 2px 8px rgba(255,215,0,0.3); }
    }
    
    @media (max-width: 767px) {
        .guide-header {
            flex-direction: column;
            text-align: center;
        }
        
        .guide-header h4 {
            margin-bottom: 10px;
        }
        
        .current-step-nav > div {
            flex-direction: column;
            text-align: center;
        }
        
        .step-item > div {
            flex-direction: column;
            text-align: center;
        }
        
        .step-item button {
            margin-top: 10px;
            width: 100%;
        }
    }
    </style>

    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('applyform/apply/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('applyform/apply/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('applyform/apply/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>

                        <div class="dropdown btn-group {:$auth->check('applyform/apply/multi')?'':'hide'}">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> {:__('More')}</a>
                            <ul class="dropdown-menu text-left" role="menu">
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=normal"><i class="fa fa-eye"></i> {:__('Set to normal')}</a></li>
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=hidden"><i class="fa fa-eye-slash"></i> {:__('Set to hidden')}</a></li>
                            </ul>
                        </div>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover"
                           data-operate-edit="{:$auth->check('applyform/apply/edit')}"
                           data-operate-del="{:$auth->check('applyform/apply/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
