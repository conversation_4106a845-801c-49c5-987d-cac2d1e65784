<?php

namespace app\admin\model;
use think\Model;
use fast\Tree;

class Apply extends Model
{
    // 表名
    public $name = 'apply_form';
    public $prefix_new = 'eb_';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    public $data;


    public function getStart_timeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['start_time'];
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setStart_timeAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function getEnd_timeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['end_time'];
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setEnd_timeAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function getAdd_timeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['add_time'];
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setAdd_timeAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function getCreate_timeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['create_time'];
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setCreate_timeAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function getUpdate_timeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['update_time'];
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setUpdate_timeAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function getDelete_timeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['delete_time'];
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setDelete_timeAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function getDead_timeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['dead_time'];
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setDead_timeAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function getTable_idTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getSchool_idTextAttr($value, $data=array())
    {
        $s=array();
        $content=[];
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getIs_realTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "0"=>"否",
            "1"=>"是",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getIs_closeTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "0"=>"未关闭",
            "1"=>"关闭",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getIs_openedTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "0"=>"未开启",
            "1"=>"已开启",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getIs_finishedTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "0"=>"未结束",
            "1"=>"已结束",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getIs_user_endTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "0"=>"否",
            "1"=>"是",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getIs_endTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "0"=>"未截止",
            "1"=>"已截止",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getApply_typeTextAttr($value,$data=[]){
        $status = [0 => __('预报名'), 1 => __('报名缴费'), 2 => __('报名缴费2'), 3 => __('报名+预约')];
        return isset($status[$value]) ? $status[$value] : $value;
    }

}