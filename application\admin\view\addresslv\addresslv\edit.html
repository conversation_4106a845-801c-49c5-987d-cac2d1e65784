<form id="edit-form" class="form-horizontal" style="position:relative;background: #fff" role="form" data-toggle="validator" method="POST" action="">
    
    <div class="panel-body">
        <div id="myTabContent" class="tab-content" >
            <div class="tab-pane fade active in" id="tabs_0">
                <input type="hidden" id="row" name="data" value='<?php echo json_encode($row);?>' />

                {if $admin['group_id']==3}
                <input type="hidden" id="h-rel_type" name="row[rel_type]" value="1">
                <input type="hidden" id="h-rel_id" name="row[rel_id]" value="{$admin['school_id']}">
                {elseif $admin['group_id']==8}
                <input type="hidden" id="h-rel_type" name="row[rel_type]" value="2">
                <input type="hidden" id="h-rel_id" name="row[rel_id]" value="{$admin['community_id']}">
                {else}
                <div class="form-group">
                    <label for="c-rel_type" class="control-label col-xs-12 col-sm-2">主体类型:</label>
                    <div class="col-xs-12 col-sm-8" id="div-rel_type">
                        <select name="row[rel_type]" id="c-rel_type"  class="form-control selectpicker"  data-rule="" data-tip="主体类型" >
                            <option value="1" >学校</option>
                            <option value="2" >社区</option>
                            <option value="3" >校外</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-rel_id" class="control-label col-xs-12 col-sm-2">主体:</label>
                    <div class="col-xs-12 col-sm-8" id="div-rel_id">
                        <select name="row[rel_id]" id="c-rel_id"  class="form-control"  data-tip="主体" >
                        </select>
                    </div>
                </div>
                {/if}


                <div class="form-group">
                    <label for="c-cityarea_id" class="control-label col-xs-12 col-sm-2">行政区划:</label>
                    <div class="col-xs-12 col-sm-8" id="div-cityarea_id" >
                        <div style="margin-top:-2px;margin-left: -2px;">
                            <select id="c-cityarea_id" id="province" class="form-control liandong"  name="row[province_id]" data-source="/cityarea/cityarea/getAreas?level=0&parent_id=0&id={$row.province_id}"
                                    type="text" value="{$row.province_id}">

                            </select>
                            <select id="city" class="form-control liandong"  name="row[city_id]" data-source="/cityarea/cityarea/getAreas?level=1&parent_id={$row.province_id}&id={$row.city_id}"
                                    type="text" value="{$row.city_id}">

                            </select>
                            <select id="county" class="form-control liandong"  name="row[district_id]" data-source="/cityarea/cityarea/getAreas?level=2&parent_id={$row.city_id}&id={$row.district_id}"
                                    type="text" value="{$row.district_id}">

                            </select>
                        </div>                        </div>
                </div>

                   <div class="form-group">
                        <label for="c-address_lv_pid" class="control-label col-xs-12 col-sm-2">父ID:</label>
                        <div class="col-xs-12 col-sm-8" id="div-pid" >
                           <select name="row[pid]" id="c-address_lv_pid"  class="form-control"  data-rule="" data-tip="父ID" >
                           </select>
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-name" class="control-label col-xs-12 col-sm-2">名称:</label>
                        <div class="col-xs-12 col-sm-8" id="div-name" >
                            <input type="text" id="c-name" name="row[name]" value="{$row.name}" class="form-control" title="name"  placeholder="" data-rule=""  data-tip="名称"  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-starttime" class="control-label col-xs-12 col-sm-2">使用期限开始:</label>
                        <div class="col-xs-12 col-sm-8" id="div-starttime" >                          <div class="form-inline">
                                <input type="text"  id="c-starttime" name="row[starttime]" value="{$row.starttime}" class="form-control datetimepicker"  data-date-format="YYYY-MM-DD" data-tip="使用期限开始" data-rule=""  />
                            </div>
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-endtime" class="control-label col-xs-12 col-sm-2">使用期限结束:</label>
                        <div class="col-xs-12 col-sm-8" id="div-endtime" >                          <div class="form-inline">
                                <input type="text"  id="c-endtime" name="row[endtime]" value="{$row.endtime}" class="form-control datetimepicker"  data-date-format="YYYY-MM-DD" data-tip="使用期限结束" data-rule=""  />
                            </div>
                        </div>
                   </div>

                   <div class="form-group">
                        <label  class="control-label col-xs-12 col-sm-2">实体或虚拟:</label>
                        <div class="col-xs-12 col-sm-8" id="div-entvirt" >
                            <label for="row[entvirt]-1"><input id="row[entvirt]-1" name="row[entvirt]" type="radio" value="1" data-tip="实体或虚拟" {if $row["entvirt"]=="1"}checked{/if} /> 实体</label>

                            <label for="row[entvirt]-2"><input id="row[entvirt]-2" name="row[entvirt]" type="radio" value="2" data-tip="实体或虚拟" {if $row["entvirt"]=="2"}checked{/if} /> 虚拟</label>
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-status" class="control-label col-xs-12 col-sm-2">状态:</label>
                        <div class="col-xs-12 col-sm-8" id="div-status" >                    
                            <input id="c-status" name="row[status]" type="hidden" value="{if $row["status"]=="0" or $row["status"]==""}0{/else}1{/if}">
                            <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-status" data-yes="1" data-no="0">
                                <i class="fa fa-toggle-on text-success fa-flip-horizontal fa-2x"></i>
                            </a>
                            <div data-favisible="switch=1" class="p-3">已开户</div>                        </div>
                   </div>
                <div class="form-group layer-footer">
                    <label class="control-label col-xs-12 col-sm-2"></label>
                    <div class="col-xs-12 col-sm-8">
                        <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
                        <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                    </div>
                </div>
            </div>
            
        </div>
    </div>
</form>

