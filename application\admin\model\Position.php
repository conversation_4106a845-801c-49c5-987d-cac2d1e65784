<?php

namespace app\admin\model;
use think\Model;
use fast\Tree;

class Position extends Model
{
    // 表名
    public $name = 'unit_position';
    public $prefix_new = 'fa_';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    public $data;

    protected function setAdmin_idAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getAdmin_idTextAttr($value, $data)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("Admin")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['username'];
        }
        return join(",",$s);
    }

    protected function setUnit_idAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getUnit_idTextAttr($value, $data)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("Unit")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }
}