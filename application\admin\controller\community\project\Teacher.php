<?php
namespace app\admin\controller\community\project;

use app\common\controller\Backend;

class Teacher extends Backend
{
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = model('CommunityTeacher');
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    public function index()
    {
        if ($this->request->isAjax()) {
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                ->where($where)
                ->order($sort, $order)
                ->count();

            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();

            $result = array("total" => $total, "rows" => $list);
            return json($result);
        }
        return $this->view->fetch();
    }

    public function approve()
    {
        $ids = $this->request->post("ids");
        if ($ids) {
            $this->model->where('id', 'in', $ids)->update(['status' => 1]);
            $this->success();
        }
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }

    public function reject()
    {
        $ids = $this->request->post("ids");
        if ($ids) {
            $this->model->where('id', 'in', $ids)->update(['status' => 2]);
            $this->success();
        }
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }
}