<?php

namespace app\admin\validate;

use think\Validate;

class Coursetable extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'name' => 'require|length:2,100',
        'school_id' => 'require|integer|gt:0',
        'ocation_id' => 'require',
        'classes_id' => 'require',
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    /**
     * 提示消息
     */
    protected $message = [
        'name.require' => '项目名称必填',
        'name.length' => '项目名称长度必须在2-100个字符之间',
        'school_id.require' => '学校必选',
        'school_id.integer' => '学校ID必须为整数',
        'school_id.gt' => '请选择有效的学校',
        'ocation_id.require' => '地点必选',
        'classes_id.require' => '班级必选',
        'start_date.date' => '开始日期格式不正确',
        'end_date.date' => '结束日期格式不正确',
    ];

    /**
     * 字段描述
     */
    protected $field = [
        'name' => '项目名称',
        'school_id' => '学校',
        'ocation_id' => '地点',
        'classes_id' => '班级',
        'start_date' => '开始日期',
        'end_date' => '结束日期',
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'add' => [
            'name' => 'require|length:2,100',
            'school_id' => 'require|integer|gt:0',
            'ocation_id' => 'require',
            'classes_id' => 'require',
        ],
        'edit' => [
            'name' => 'require|length:2,100',
            'school_id' => 'require|integer|gt:0',
            'ocation_id' => 'require',
            'classes_id' => 'require',
        ]
    ];

    /**
     * 自定义验证方法：验证地点数组
     */
    protected function checkOcationIds($value, $rule, $data)
    {
        if (is_array($value)) {
            return count($value) > 0;
        }
        return !empty($value);
    }

    /**
     * 自定义验证方法：验证班级数组
     */
    protected function checkClassesIds($value, $rule, $data)
    {
        if (is_array($value)) {
            return count($value) > 0;
        }
        return !empty($value);
    }
}
