<?php

namespace app\admin\controller\user;

use app\common\controller\Backend;
use app\common\library\Auth;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 会员管理
 *
 * @icon fa fa-user
 */
class User extends Backend
{

    protected $relationSearch = true;
    protected $searchFields = 'id,username,nickname';

    /**
     * @var \app\admin\model\User
     */
    protected $model = null;
    protected $noNeedRight = ["lists", "status","changeStatus"];
    protected $noNeedLogin = ["lists", "status","changeStatus"];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\User;
    }

       /**
     * 查看
     */
    public function index()
    {
        $this->request->filter(['strip_tags', 'trim']);

        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            [$where, $sort, $order, $offset, $limit] = $this->buildparams();

            $groupId = $this->request->param('group_id');
            $query = $this->model->with('group');

            if ($groupId !== '' && $groupId !== null) {
                $query = $query->where('group_id', $groupId);
            }

            $list = $query
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            $result = ['total' => $list->total(), 'rows' => $list->items()];

            foreach ($result['rows'] as &$row) {
                $row['avatar'] = $row['avatar'] ? cdnurl($row['avatar'], true) : letter_avatar($row['nickname']);
                unset($row['password'], $row['salt']);
            }

            return json($result);
        }

        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            //$this->token();
        }
        return parent::add();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        if ($this->request->isPost()) {
            //$this->token();
        }
        $row = $this->model->get($ids);

        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $this->view->assign('groupList', build_select('row[group_id]', \app\admin\model\UserGroup::column('id,name'), $row['group_id'], ['class' => 'form-control selectpicker']));
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $row=json_decode(json_encode($row),1);
            $row["jointime"]=date("Y-m-d H:i:s",$row["jointime"]);
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }

        $this->success();
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        model('User')->where('id', $ids)->delete();
        $this->success();
    }


    public function lists($ids = NULL){
        $where="where 1=1 ";
        $page=1;
        $pagesize=50;
        if(isset($_REQUEST['q'])&&$_REQUEST['q']<>""){
            $where.=" and ((username like '%".trim($_REQUEST['q'])."%' or mobile like '%".trim($_REQUEST['q'])."%')";
        }
        if(isset($_REQUEST["groupId"]) && $_REQUEST["groupId"] != ""){
            if(isset($_REQUEST['q'])&&$_REQUEST['q']<>""){
                $where.=")";
            }else{
                $where.=" and  (group_id=".$_REQUEST['groupId'].")";
            }
        }else{
            $where.=" and (group_id=1 or group_id=2 or group_id=3)";
        }
        $sql="select id value,username text,mobile phone,group_id from fa_user $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
        $list = db()->query($sql);

        // 根据args参数收集已选中的记录集
        $leftOptions = [];
        if (isset($_POST["args"])) {
            $args = $_POST["args"];
            $studentIds = (isset($args[0])&&$args[0]!="[]") ? $args[0] : "";
            $teacherIds = (isset($args[1])&&$args[1]!="[]") ? $args[1] : "";
            $managerIds = (isset($args[2])&&$args[2]!="[]") ? $args[2] : "";
            if (!empty($studentIds)) {
                $students = db()->query("select id value, username text, mobile phone, group_id from dev002_gxyusheng.fa_user where id in (" . join(',', $studentIds) . ")");
                $leftOptions = array_merge($leftOptions, $students);
            }
            if (!empty($teacherIds)) {
                $teachers = db()->query("select id value, username text, mobile phone, group_id from dev002_gxyusheng.fa_user where id in (" . join(',', $teacherIds) . ")");
                $leftOptions = array_merge($leftOptions, $teachers);
            }
            if (!empty($managerIds)) {
                $managers = db()->query("select id value, username text, mobile phone, group_id from dev002_gxyusheng.fa_user where id in (" . join(',', $managerIds) . ")");
                $leftOptions = array_merge($leftOptions, $managers);
            }
        }
        $leftValues = array_column($leftOptions, 'value');
        $list = array_filter($list, function ($item) use ($leftValues) {
            return !in_array($item['value'], $leftValues);
        });
        $total = db()->query("select count(1) as c from dev002_gxyusheng.eb_class_teacher t inner join dev002_gxyusheng.fa_user u on t.teacher_id=u.id $where")[0];
        echo json_encode(array("list" => $list, "total" => $total['c'], "leftOptions" => $leftOptions));
        exit;
    }

    public function changeStatus($ids = null)
    {
        $row = $this->model->get($_REQUEST['id']);
        if($_REQUEST['status']=="normal"){
            $row->status = "normal";
        }else{
            $row->status = $_REQUEST['status'];
        }
        $row->id=$_REQUEST['id'];
        $row->save();
        $this->success("状态已经更新成功！");
    }

}