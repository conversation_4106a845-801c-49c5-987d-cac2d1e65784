<?php

namespace app\admin\controller\coursetable;

use app\common\controller\Backend;
use fast\Tree;
/**
 * 
 *
 * @icon fa fa-file
 */
class Classplant extends Backend
{
    
    /**
     * Classplant模型对象
     */
    protected $model = null;
    protected $noNeedRight=["lists","status","export","export2"];
    protected $noNeedLogin=["lists","status","export","export2"];


    public function _initialize()
    {
        parent::_initialize();
        $this->shopid=false;
        $this->model = model('Classplant');
        $this->modelValidate = true;
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            $tb=model("Coursetable")->where("id","=",$this->request->param("table_id"))->select();
            if($tb) {
                $tb = $tb[0];
                $ids=explode(",",$tb['classes_id']);
                for($i=0;$i<count($ids);$i++) {
                    //$classes=model("Classes")->where("status","=",1)->where("id","=",$ids[$i])->find();
                    $classes=model("Classes")->alias("c")->join("fa_course_classroomeplan r","r.classes_id=c.id and r.table_id='{$this->request->param("table_id")}'","left")->where("r.status","=",1)->where("c.status","=",1)->where("c.id","=",$ids[$i])->find();
                    if($classes&&!empty($classes)){
                        $ocouser_count=0;
                        $ocation_type = 1;
                        if(preg_match("/舞|瑜伽|太极|礼仪|仪态|八段锦/",$classes['name'])){
                            $ocation_type=3;
                        }elseif(preg_match("/书|画/",$classes['name'])) {
                            $ocation_type = 2;
                        }elseif(preg_match("/声乐|摄影|器乐|古筝/",$classes['name'])) {
                            $ocation_type = "1,4,3";
                        }else{
                            $ocation_type = 1;
                        }
                        if($classes['addresslv_id']==2){
                            $ocouser_count=17;
                        }else if($classes['addresslv_id']==3){
                            $ocouser_count=8;
                        }else{
                            $ocouser_count=12;
                        }
                        $data=array(
                            "classes_id"=>$ids[$i],
                            "table_id"=>$tb['id'],
                            "subject_lv"=>$classes['subject_lv'],
                            "addresslv_id"=>$classes['addresslv_id'],
                            "classes_per_week"=>1,
                            "same_classroom"=>1,
                            "same_ocation"=>1,
                            "can_same_day"=>0,
                            "daytime"=>"1,3",
                            "status"=>"1",
                            "ocourse_count"=>$ocouser_count,
                            "ocation_type"=>$ocation_type,
                        );
                        model("Classplant")->insertUpdate($data,["table_id","classes_id"],false);
                    }
                }
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $where="";
            $total = $this->model->alias("classplant")->join("eb_classes classes", "classplant.classes_id=classes.id","left")
                ->join("fa_course_classroomeplan r","r.classes_id=classplant.classes_id and r.table_id='{$this->request->param("table_id")}'","left")
                ->where("classplant.status", "=", 1)
                ->field("classplant.*,classes.name,r.ocation_id,r.week,r.daytime");
            $total = $total->where("classplant.status", "<>", -1);
            $total = $total->where("classplant.classes_id", "in", $ids);
            if(isset($_REQUEST['search'])&&$_REQUEST['search']){
                $total = $total->where("classes.name", "like", "%" . $_REQUEST['search'] . "%");
            }
            $total = $total
                ->count();
            $list = $this->model->alias("classplant")->join("eb_classes classes", "classplant.classes_id=classes.id","left")
                ->join("fa_course_classroomeplan r","r.classes_id=classplant.classes_id and r.table_id='{$this->request->param("table_id")}'","left")
                ->where("classplant.status", "=", 1)
                ->field("classplant.*,classes.name,r.ocation_id,r.week,r.daytime");
            if(isset($_REQUEST['search'])&&$_REQUEST['search']){
                $list = $list->where("classes.name", "like", "%" . $_REQUEST['search'] . "%");
            }
            $list = $list->where("classplant.classes_id", "in", $ids);
            //if ($_SESSION['think']['admin']['group_id'] == 2) {
            //    $list = $list->where("city_id", "=", $_SESSION['think']['admin']['city_id']);
            //}
            $list = $list->where("classplant.status", "<>", -1)
                ->order('weigh desc,classplant.id desc')
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();
            //$this->model->getLastSql();
            $list = collection($list)->toArray();
            function bool($int){
                return $int?"是":"否";
            }
            for($i=0;$i<count($list);$i++){
                $list[$i]['classes_id']=model("Classes")->getClasses_idTextAttr($list[$i]['classes_id'],$list[$i])."({$list[$i]['classes_id']})";
                $list[$i]['subject_lv']=model("Subjectlv")->getSubject_lvTextAttr($list[$i]['subject_lv'],$list[$i]);
                $list[$i]['ocation_type']=model("Ocation")->getOcation_typeTextAttr($list[$i]['ocation_type'],$list[$i]);
                $list[$i]['manager_id']=model("User")->getTeacher_idTextAttr($list[$i]['manager_id'],$list[$i]);
                $list[$i]['disable_week_range']=$this->model->getDisable_week_rangeTextAttr($list[$i]['disable_week_range'],$list[$i]);
                $list[$i]['must_week_range']=$this->model->getMust_week_rangeTextAttr($list[$i]['must_week_range'],$list[$i]);
                $list[$i]['ocation_id']=model("Ocation")->getOcation_idTextAttr($list[$i]['ocation_id'],$list[$i])."({$list[$i]['ocation_id']})";
                $list[$i]['can_same_day']=bool($list[$i]['can_same_day']);
                $list[$i]['same_classroom']=bool($list[$i]['same_classroom']);
                $list[$i]['same_ocation']=bool($list[$i]['same_ocation']);
            }
            $result = array("total" => $total, "rows" => $list);
            return json($result);
        }
        $this->assignconfig("relType",$_REQUEST['relType']);
        $this->assignconfig("table_id",$_REQUEST['table_id']);
        $this->assignconfig("admin",$_SESSION['think']['admin']);
        return $this->view->fetch();
    }
    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        //$this->model->validate($validate);
                    }
                    //班级是否已经有课程安排
                    $classplant=model("Classplant")->where("classes_id",$params['classes_id'])->where("status",1)->find();
                    if($classplant){
                        $this->error("该班级已经有课程安排");
                    }
                    $datatime=date("Y-m-d H:i:s");
                    $params['create_time']=$datatime;
                    unset($params['delete_time']);
                    unset($params['update_time']);
//                    if($_SESSION['think']['admin']['group_id']==2){
//                        $params['city_id']=$_SESSION['think']['admin']['city_id'];
//                    }
                    $params['exclint_day']=(!array_key_exists("exclint_day",$params)||$params['exclint_day']=="")?date("Y-m-d",time()):date("Y-m-d",strtotime($params['exclint_day']));
                    $params['daytime']=join(",",$params['daytime']);
                    $params['daytimeall']=join(",",$params['daytimeall']);
                    $params['disable_week_range']=join(",",$params['disable_week_range']);
                    $params['must_week_range']=join(",",$params['must_week_range']);
                    $params['ocation_type']=join(",",$params['ocation_type']);
                    $params['teacher_id']=join(",",$params['teacher_id']);
                    $params['manager_id']=join(",",$params['manager_id']);
                    $params['table_id']=$params['table_id'];
                    $result = $this->model->allowField(true)->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($this->model->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->assignconfig("admin",$_SESSION['think']['admin']);
        // 生成 leftOptions 和 rightOptions 的数据
        $data=array("args"=>[[], [],[]]);
        $leftOptions = $this->generateOptions('/user/user/lists',$data);
        $leftOptions = ($leftOptions==null)?[]:$leftOptions["leftOptions"];
        $rightOptions = $this->generateOptions('/user/user/lists');
        $rightOptions = ($rightOptions==null)?[]:$rightOptions["list"];
        $this->assignconfig("leftOptions", $leftOptions);
        $this->assignconfig("rightOptions", $rightOptions);
        $this->assign("table_id",$_REQUEST['table_id']);
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = NULL)
    {
        $row = $this->model->get($ids);
        $classes=model("Classes")->where("id",$row['classes_id'])->find();
        $address_lv=$classes['addresslv_id'];
        if (!$row)
            $this->error(__('No Results were found'));
            $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        //$row->validate($validate);
                    }
                    $datatime=date("Y-m-d H:i:s");
                    $params['update_time']=$datatime;
                    unset($params['delete_time']);
                    unset($params['create_time']);
                    $params['teacher_id']=join(",",$params["teacher_id"]);
                    $params['manager_id']=join(",",$params["manager_id"]);
                    $params['address_lv']=$address_lv;
                    $params['exclint_day']=(!array_key_exists("exclint_day",$params)||$params['exclint_day']=="")?date("Y-m-d",time()):date("Y-m-d",strtotime($params['exclint_day']));
                    $params['daytime']=join(",",$params['daytime']);
                    $params['daytimeall']=join(",",$params['daytimeall']);
                    $params['disable_week_range']=join(",",$params['disable_week_range']);
                    $params['must_week_range']=join(",",$params['must_week_range']);
                    $params['ocation_type']=join(",",$params['ocation_type']);
                    $params['table_id']=$params['table_id'];
                    $result = $row->allowField(true)->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($row->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $r=model("classes")->where("id",$row['classes_id'])->find();
        $this->assignconfig("admin",$_SESSION['think']['admin']);
        $this->view->assign("row", $row);
        $row['teacher_id']=(isset($row['teacher_id'])&&$row['teacher_id']!=null)?explode(",",$row['teacher_id']):array();
        $row['manager_id']=(isset($row['manager_id'])&&$row['manager_id']!=null)?explode(",",$row['manager_id']):array();
        $data=array("args"=>[[],$row['teacher_id'], $row['manager_id']]);
        $leftOptions = $this->generateOptions('/user/user/lists',$data);
        $leftOptions = ($leftOptions==null)?[]:$leftOptions["leftOptions"];
        $rightOptions = $this->generateOptions('/user/user/lists');
        $rightOptions = ($rightOptions==null)?[]:$rightOptions["list"];
        $this->assignconfig("leftOptions", $leftOptions);
        $this->assignconfig("rightOptions", $rightOptions);
        $this->assign("address_lv",$address_lv);
        $this->assign("table_id",$row["table_id"]?$row["table_id"]:trim($_REQUEST['table_id']));
        return $this->view->fetch();

    }

    /**
     * 添加
     */
    public function view($ids = NULL)
    {
        $row=$this->model->getRow($ids);
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    public function lists($ids = NULL){
        $where="where 1=1";
        if($_SESSION['think']['admin']['group_id']!=1){
            $where.=" and city_id='{$_SESSION['think']['admin']['city_id']}'";
        }
        $page=1;
        $pagesize=50;
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                $keyField = $this->request->request("keyField");
                $keyValue = $this->request->request("keyValue");
                $page = $this->request->request("pageNumber");
                $page = ($page == null) ? 1 : $page;
                $pagesize = $this->request->request("pageSize");
                $pagesize = ($pagesize == null) ? 10 : $pagesize;
                if ($keyValue != "") {
                    $nn=preg_split("/\,/",$keyValue);
                    for($i=0;$i<count($nn);$i++) {
                        $where.= " and $keyField='{$nn[$i]}'";
                    }
                }
            }else{
                $where="where 1=1";
                if(isset($_REQUEST['city_id'])&&$_REQUEST['city_id']>0){
                    //$where.=" and city_id=".$_REQUEST['city_id'];
                }
            }
        }
        $sql="select id,__firstfield__ name from dev002_gxyusheng.fa_course_table_classplant $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
        $list = db()->query($sql);

//        $r_name = model("School")->field("id,name")->where("id", "=", $_REQUEST['school_id'])->select();
//        if (empty($list)&&!empty($r_name)) {
//            $data = array(
//                "type" => "file",
//                "pid" => 0,
//                "name" => $r_name[0]['name']."学科",
//                "title" => $r_name[0]['name'],
//                "ismenu" => 0,
//                "createtime" => time(),
//                "updatetime" => time(),
//                "status" => 1,
//                "school_id" => $_REQUEST['school_id'],
//            );
//            $this->model->create($data);
//        }
//        $sql="select id,pid,name,title,status from dev002_gxyusheng.fa_course_table_classplant $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
//        $list = db()->query($sql);
//        $tree = Tree::instance();
//        $tree->init($list, 'pid');
//        $list = $tree->getTreeList($tree->getTreeArray(0), 'name');

        $total = db()->query("select count(1) as c from  dev002_gxyusheng.fa_course_table_classplant $where")[0];
        echo json_encode(array("list" => $list, "total" => $total['c']));
        exit;
    }

    public function status($ids = '')
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }
        try {
            $result=$this->model->where("id","=",$ids)->field("status")->find();
            $v=$result->status==1?0:1;
            $this->model->where("id","=",$ids)->update(["status"=>$v]);
            $this->success('切换成功!');
        } catch (Exception $e) {
            $this->error('切换失败!'.$e->getMessage());
        }
    }

    public function changeStatus($ids = '')
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }
        try {
            $result=$this->model->where("id","=",$ids)->field("status")->find();
            $v=$result->status==1?0:1;
            $this->model->where("id","=",$ids)->update(["status"=>$v]);
            $this->success('切换成功!');
        } catch (Exception $e) {
            $this->error('切换失败!'.$e->getMessage());
        }
    }

    public function export()
    {
        // 获取数据
        $list = $this->model->select();

        // 设置导出文件名
        $filename = '教学点下载' . date('YmdHis');

        // 设置表头
        $headers = [
            'ID', '项目名称', '班级'
        ];

        // 设置数据
        $data = [];
        foreach ($list as $item) {
            $ocation=model("Ocation")->get($item['id']);
            $courseable=model("Coursetable")->get($item['table_id']);
            $classes=model("Classes")->get($item['classes_id']);
            if($ocation&&$courseable&&$classes) {
                $data[] = [
                    $ocation['name'],
                    $courseable['name'],
                    $classes['name'],
                ];
            }
        }

        // 调用导出方法
        $this->exportExcel($filename, $headers, $data);
    }


    public function export2()
    {
        // 获取数据
        $list = $this->model->select();

        // 设置导出文件名
        $filename = '教学点下载' . date('YmdHis');

        // 设置表头
        $headers = [
            'Id',
            '项目名称',
            '班名',
            '教室',
            '课程',
            '老师',
            '日期',
            '周几',
            '时段',
            '课节',
            '备注',
            '操作',
        ];

// 设置数据
        $data = [];
        foreach ($list as $item) {
            $ocation = model("Ocation")->field("name")->get($item['id']);
            $courseable = model("Coursetable")->field("name")->get($item['table_id']);
            $classes = model("Classes")->field("name")->get($item['classes_id']);
            $data[] = [
                $item['id'],
                $item['project_name'],
                $classes['name'],
                $ocation['name'],
                $courseable['name'],
                $item['teacher_id'],
                $item['date'],
                $item['day_of_week'],
                $item['time_slot'],
                $item['class_period'],
                $item['remarks'],
                $item['operation'],
            ];
        }

        // 调用导出方法
        $this->exportExcel($filename, $headers, $data);
    }

    protected function exportExcel($filename, $headers, $data)
    {
        // 导出Excel文件
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        // 设置表头
        $sheet->fromArray($headers, null, 'A1');
        // 设置数据
        $sheet->fromArray($data, null, 'A2');

        // 设置列宽为内容自适应
        foreach (range('A', $sheet->getHighestColumn()) as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        // 设置响应头
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
        header('Cache-Control: max-age=0');
        // 输出文件
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

}
