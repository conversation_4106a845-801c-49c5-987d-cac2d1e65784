<?php
namespace app\admin\validate;

use think\Validate;

class Classes extends Validate
{
    /**
     * 定义验证规则
     * 格式：'字段名'    =>    ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'row.name' => 'require',
        'row.nianji' => 'require',
        'row.xueqi' => 'require',
        'row.qishu' => 'require',
        'row.num' => 'require|integer',
        'row.price' => 'require|number|>=:0',
        'row.rel_type' => 'require',
        'row.classes_type' => 'require',
        // 可以根据实际情况添加 subject_lv 和 class_type 的验证
        // 'row.subject_lv' => 'require',
        // 'row.class_type' => 'require',
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名'    =>    '错误信息'
     *
     * @var array
     */
    protected $message = [
        'row.name.require' => '班级名称不能为空',
        'row.nianji.require' => '年级不能为空',
        'row.xueqi.require' => '学期不能为空',
        'row.qishu.require' => '期数不能为空',
        'row.num.require' => '班级人数不能为空',
        'row.num.integer' => '班级人数必须为整数',
        'row.price.require' => '学费不能为空',
        'row.price.number' => '学费必须为数字',
        'row.price.>=' => '学费不能为负数',
        'row.rel_type.require' => '主体类型必须选择',
        'row.classes_type.require' => '班级类型必须选择',
        // 可以根据实际情况添加 subject_lv 和 class_type 的错误信息
        // 'row.subject_lv.require' => '课程名称必须选择',
        // 'row.class_type.require' => '课程类型必须选择',
    ];
}