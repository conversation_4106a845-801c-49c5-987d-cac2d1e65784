<?php

namespace app\admin\model\community\project;

use think\Model;

class Setcourse extends Model
{
    // 表名
    protected $name = 'community_project_setcourse';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 追加属性
    protected $append = [];

    // 可写入字段
    protected $field = [
        'id', 'community_id', 'school_name', 'publicwelfare_id', 'project_name', 
        'course_name', 'course_image', 'course_description', 'teaching_unit', 
        'status', 'weigh', 'createtime', 'updatetime'
    ];

    // 字段类型转换
    protected $type = [
        'course_image' => 'string',
        'course_description' => 'string'
    ];
}