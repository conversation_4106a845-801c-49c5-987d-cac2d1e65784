<form id="add-form" class="form-horizontal" style="position:relative;background: #fff" role="form" data-toggle="validator" method="POST" action="">
    
    <div class="panel-body">
        <div id="myTabContent" class="tab-content" >
            <div class="tab-pane fade active in" id="tabs_0">
                <input type="hidden" name="row[id]" value="" class="form-control" data-rule="" data-tip="ID"  />

                {if $admin['group_id']==3}
                <input type="hidden" id="h-rel_type" name="row[rel_type]" value="1">
                <input type="hidden" id="h-rel_id" name="row[rel_id]" value="{$admin['school_id']}">
                {elseif $admin['group_id']==8}
                <input type="hidden" id="h-rel_type" name="row[rel_type]" value="2">
                <input type="hidden" id="h-rel_id" name="row[rel_id]" value="{$admin['community_id']}">
                {else}
                <div class="form-group">
                    <label for="c-rel_type" class="control-label col-xs-12 col-sm-2">主体类型:</label>
                    <div class="col-xs-12 col-sm-8" id="div-rel_type">
                        <select name="row[rel_type]" id="c-rel_type"  class="form-control selectpicker"  data-rule="" data-tip="主体类型" >
                            <option value="1" >学校</option>
                            <option value="2" >社区</option>
                            <option value="3" >校外</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-rel_id" class="control-label col-xs-12 col-sm-2">主体:</label>
                    <div class="col-xs-12 col-sm-8" id="div-rel_id">
                        <select name="row[rel_id]" id="c-rel_id"  class="form-control"  data-tip="主体" >
                        </select>
                    </div>
                </div>
                {/if}

                <div class="form-group">
                    <label for="c-cityarea_id" class="control-label col-xs-12 col-sm-2">行政区划:</label>
                    <div class="col-xs-12 col-sm-8" id="div-cityarea_id" >
                        <div style="margin-top:-2px;margin-left: -2px;">
                            <select id="c-cityarea_id" id="province" class="form-control liandong"  name="row[province_id]"
                                    type="text" value="">

                            </select>
                            <select id="city" class="form-control liandong"  name="row[city_id]"
                                    type="text" value="">

                            </select>
                            <select id="county" class="form-control liandong"  name="row[district_id]"
                                    type="text" value="">

                            </select>
                        </div>                        </div>
                </div>

                   <div class="form-group">
                        <label for="c-address_lv_pid" class="control-label col-xs-12 col-sm-2">父ID:</label>
                        <div class="col-xs-12 col-sm-8" id="div-pid">
                           <select name="row[pid]" id="c-address_lv_pid"  class="form-control"  data-rule="" data-tip="父ID" >
                           </select>
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-name" class="control-label col-xs-12 col-sm-2">名称:</label>
                        <div class="col-xs-12 col-sm-8" id="div-name">
                            <input type="text" id="c-name" name="row[name]" value="" class="form-control" title="name"  placeholder="" data-rule=""  data-tip="名称"  />
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-starttime" class="control-label col-xs-12 col-sm-2">使用期限开始:</label>
                        <div class="col-xs-12 col-sm-8" id="div-starttime">                          <div class="form-inline">
                                <input type="text"  id="c-starttime" name="row[starttime]" value="<?php echo date('Y-m-d');?>" class="form-control datetimepicker"  data-date-format="YYYY-MM-DD" data-tip="使用期限开始" data-rule=""  />
                            </div>
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-endtime" class="control-label col-xs-12 col-sm-2">使用期限结束:</label>
                        <div class="col-xs-12 col-sm-8" id="div-endtime">                          <div class="form-inline">
                                <input type="text"  id="c-endtime" name="row[endtime]" value="<?php echo date('Y-m-d');?>" class="form-control datetimepicker"  data-date-format="YYYY-MM-DD" data-tip="使用期限结束" data-rule=""  />
                            </div>
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-entvirt" class="control-label col-xs-12 col-sm-2">实体或虚拟:</label>
                        <div class="col-xs-12 col-sm-8" id="div-entvirt">
                            <label for="row[entvirt]-1"><input id="row[entvirt]-1" name="row[entvirt]" type="radio" value="1" data-tip="实体或虚拟" checked/> 实体</label>
                            <label for="row[entvirt]-2"><input id="row[entvirt]-2" name="row[entvirt]" type="radio" value="2" data-tip="实体或虚拟"  /> 虚拟</label>
                        </div>
                   </div>

                   <div class="form-group">
                        <label for="c-status" class="control-label col-xs-12 col-sm-2">状态:</label>
                        <div class="col-xs-12 col-sm-8" id="div-status">                    
                            <input id="c-status" name="row[status]" type="hidden" value="1">
                            <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-status" data-yes="1" data-no="0">
                                <i class="fa fa-toggle-on text-success fa-flip-horizontal fa-2x"></i>
                            </a>
                            <div data-favisible="switch=1" class="p-3">已开户</div>                        </div>
                   </div>
                <div class="form-group layer-footer">
                    <label class="control-label col-xs-12 col-sm-2"></label>
                    <div class="col-xs-12 col-sm-8">
                        <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
                        <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                    </div>
                </div>
            </div>
            
        </div>
    </div>
</form>



