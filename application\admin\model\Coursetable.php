<?php

namespace app\admin\model;

use think\Model;
use fast\Tree;

class Coursetable extends Model
{
    // 表名
    public $name = 'course_table';
    public $prefix_new = 'fa_';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    public $data;

    public function getStart_dateTextAttr($value, $data)
    {
        $value = $value ? $value : $data['start_date'];
        return is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    public function getCoursetable_idTextAttr($value, $data = array())
    {
        $s = array();
        $content = [];
        if ($value) {
            $nn = preg_split("/,/", $value);
        } else {
            $nn = array();
        }
        for ($i = 0; $i < count($nn); $i++) {
            if ($nn[$i] == "") continue;
            $r = Model("Coursetable")->get(array("id" => $nn[$i]));
            if ($r) $s[] = $r['name'];
        }
        return join(",", $s);
    }

    protected function setStart_dateAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    public function getOcation_idTextAttr($value, $data = array())
    {
        $s = array();
        $content = [];
        if ($value) {
            $nn = preg_split("/,/", $value);
        } else {
            $nn = array();
        }
        for ($i = 0; $i < count($nn); $i++) {
            if ($nn[$i] == "") continue;
            if (isset($content[$nn[$i]])) $s[] = $content[$nn[$i]];
        }
        return join(",", $s);
    }

    public function getClasses_idTextAttr($value, $data = array())
    {
        $s = array();
        $content = [];
        if ($value) {
            $nn = preg_split("/,/", $value);
        } else {
            $nn = array();
        }
        for ($i = 0; $i < count($nn); $i++) {
            if ($nn[$i] == "") continue;
            if (isset($content[$nn[$i]])) $s[] = $content[$nn[$i]];
        }
        return join(",", $s);
    }

    public function getTeacher_idTextAttr($value, $data = array())
    {
        $s = array();
        $content = [];
        if ($value) {
            $nn = preg_split("/,/", $value);
        } else {
            $nn = array();
        }
        for ($i = 0; $i < count($nn); $i++) {
            if ($nn[$i] == "") continue;
            if (isset($content[$nn[$i]])) $s[] = $content[$nn[$i]];
        }
        return join(",", $s);
    }

    public function getSchool_idTextAttr($value, $data = array())
    {
        $s = array();
        $content = [];
        if ($value) {
            $nn = preg_split("/,/", $value);
        } else {
            $nn = array();
        }
        for ($i = 0; $i < count($nn); $i++) {
            if ($nn[$i] == "") continue;
            if (isset($content[$nn[$i]])) $s[] = $content[$nn[$i]];
        }
        return join(",", $s);
    }


    public function getCourse_idTextAttr($value, $data = array())
    {
        if ($value) {
            $nn = preg_split("/,/", $value);
        } else {
            $nn = array();
        }
        $s = array();
        for ($i = 0; $i < count($nn); $i++) {
            if ($nn[$i] == "") continue;
            $r = Model("Coursetable")->get(array("id" => $nn[$i]));
            if ($r) $s[] = $r['name'];
        }
        return join(",", $s);
    }

}