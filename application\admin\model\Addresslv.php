<?php

namespace app\admin\model;
use think\Model;
use fast\Tree;

class Addresslv extends Model
{
    // 表名
    public $name = 'address_lv';
    public $prefix_new = 'fa_';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    public $rel_type;
    public $rel_id;

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    public $data;


    public function getAddress_lvTextAttr($rel_type,$value, $data)
    {
        $rel_type=$rel_type?$rel_type:1;
        $this->rel_type=$rel_type;
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        $index=1;
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("Addresslv")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
            while(!empty($r)&&$r['pid']>0&&$index++<4){
                $r = Model("Addresslv")->get(array("id" => $r['pid']));
                if($r) $s[]= $r['name'];
            }
        }
        return join(">",array_reverse($s));
    }

    public function getAddresslv_idTextAttr($value, $data=null)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        $index=1;
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("Addresslv")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
            while(!empty($r)&&$r['pid']>0&&$index++<4){
                $r = Model("Addresslv")->get(array("id" => $r['pid']));
                if($r) $s[]= $r['name'];
            }
        }
        return join(">",array_reverse($s));
    }



    public function getRel_idTextAttr($rel_type,$value, $data)
    {
        $rel_type=$rel_type?$rel_type:1;
        $this->rel_type=$rel_type;
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        $r=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if($this->rel_type==1||$this->rel_type=="学校"){
                $r = Model("School")->get(array("id" => $nn[$i]));
            }elseif($this->rel_type==2||$this->rel_type=="社区"){
                $r = Model("Community")->get(array("id" => $nn[$i]));
            }
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    protected function setRel_idAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }


    public function getStarttimeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['starttime'];
        return is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    protected function setStarttimeAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    public function getEndtimeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['endtime'];
        return is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    protected function setEndtimeAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d", $value) : $value;
    }


    public function getEntvirtTextAttr($value, $data)
    {
        $s=array();
        $content=array(
            "1"=>"实体",
            "2"=>"虚拟",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getRel_typeTextAttr($value)
    {
        $s=array();
        $content=array(
            "1"=>"学校",
            "2"=>"社区",
            "3"=>"校外",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getPidTextAttr($value, $data)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("addresslv")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    protected function setPidAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

}