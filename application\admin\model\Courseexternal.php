<?php

namespace app\admin\model;
use think\Model;
use fast\Tree;

class Courseexternal extends Model
{
    // 表名
    public $name = 'course_external';
    public $prefix_new = 'eb_';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    public $data;


    public function getCourse_startTextAttr($value, $data)
    {
        $value = $value ? $value : $data['course_start'];
        return is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    protected function setCourse_startAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    public function getCourse_endTextAttr($value, $data)
    {
        $value = $value ? $value : $data['course_end'];
        return is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    protected function setCourse_endAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    public function getCheck_timeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['check_time'];
        return is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    protected function setCheck_timeAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    public function getEvaluation_timeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['evaluation_time'];
        return is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    protected function setEvaluation_timeAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    public function getStart_dateTextAttr($value, $data)
    {
        $value = $value ? $value : $data['start_date'];
        return is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    protected function setStart_dateAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    public function getEnd_dateTextAttr($value, $data)
    {
        $value = $value ? $value : $data['end_date'];
        return is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    protected function setEnd_dateAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    public function getCourse_propertyTextAttr($value, $data)
    {
        $value = $value ? $value : $data['course_property'];
        return is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

    protected function setCourse_propertyAttr($value)
    {
        return $value && is_numeric($value) ? date("Y-m-d", $value) : $value;
    }

}