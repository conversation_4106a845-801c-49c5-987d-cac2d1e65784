<?php
namespace app\admin\model;

use think\Model;

class Activity extends Model
{
    // 数据表名（如果类名与表名一致，可以省略此属性）
    protected $name = 'eb_activity';

    // 主键字段名
    protected $pk = 'id';

    // 自动写入时间戳
    protected $autoWriteTimestamp = true;

    // 时间字段的格式化
    protected $dateFormat = 'Y-m-d H:i:s';


    /**
     * 获取活动列表
     */
    public function getActivityList()
    {
        return $this->whereNull('delete_time') // 筛选未被伪删除的活动
        ->order('start_time', 'desc') // 按开始时间倒序排列
        ->select();
    }

    /**
     * 根据 ID 获取活动详情
     */
    public function getActivityById($id)
    {
        return $this->where('id', $id)
            ->whereNull('delete_time') // 确保未被伪删除
            ->find();
    }
}