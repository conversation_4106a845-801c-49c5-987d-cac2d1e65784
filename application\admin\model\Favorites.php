<?php

namespace app\admin\model;

use think\Model;
use traits\model\SoftDelete;

class Favorites extends Model
{
    protected $autoWriteTimestamp = 'datetime';
    protected $dateFormat = 'Y-m-d H:i:s';

//    use SoftDelete;
//    protected $deleteTime = 'delete_time';

//    protected $createTime = 'create_time';
//    protected $updateTime = 'update_time';

//    protected $type = [
//        'create_time' => 'datetime',
//        'update_time' => 'datetime',
//    ];

//    protected $type = [
//        'create_time'=>'datetime:Y-m-d H:i:s',
//        'update_time'=>'datetime:Y-m-d H:i:s',
//        'delete_time'=>'datetime:Y-m-d H:i:s'
//    ];

//    protected $field = true;

//    protected $auto = ['create_time','update_time'];
    protected $insert = ['create_time','update_time'];
    protected $update = ['update_time'];

    // 自定义时间设置
//     protected function setCreateTimeAttr()
//     {
//         return date('Y-m-d H:i:s');
//     }

}