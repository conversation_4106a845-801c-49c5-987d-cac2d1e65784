<form id="edit-form" class="form-horizontal" style="position:relative;background: #fff" role="form"
      data-toggle="validator" method="POST" action="">
    <div class="panel">
        <ul class="nav nav-tabs">
            <li class="active"><a href="#tabs_0" data-toggle="tab" aria-expanded="true">班级信息</a></li>
            <li><a href="#tabs_1" data-toggle="tab" aria-expanded="true">人员设置</a></li>
            <li><a href="#tabs_2" data-toggle="tab" aria-expanded="true">开关设置</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="tabs_0">
                <input type="hidden" id="row" name="data" value='<?php echo json_encode($row);?>'/>
                {if $admin['group_id']==3}
                <input type="hidden" id="h-rel_type" name="row[rel_type]" value="1">
                <input type="hidden" id="h-rel_id" name="row[rel_id]" value="{$admin['school_id']}">

                {elseif $admin['group_id']==8}
                <input type="hidden" id="h-rel_type" name="row[rel_type]" value="2">
                <input type="hidden" id="h-rel_id" name="row[rel_id]" value="{$admin['community_id']}">
                <input type="hidden" id="c-nianji" name="row[nianji]" value="{$row.nianji}">
                <input type="hidden" id="c-xueqi" name="row[xueqi]" value="{$row.xueqi}">
                <input type="hidden" id="c-qishu" name="row[qishu]" value="{$row.qishu}">
                {else}
                <div class="form-group">
                    <label for="c-rel_type" class="control-label col-xs-12 col-sm-2">主体类型:</label>
                    <div class="col-xs-12 col-sm-8" id="div-rel_type">
                        <select name="row[rel_type]" id="c-rel_type" class="form-control selectpicker" data-rule=""
                                data-tip="主体类型">
                            <option value="1">学校</option>
                            <option value="2">社区</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="c-rel_id" class="control-label col-xs-12 col-sm-2">主体:</label>
                    <div class="col-xs-12 col-sm-8" id="div-rel_id">
                        <select name="row[rel_id]" id="c-rel_id" class="form-control" data-tip="主体">
                        </select>
                    </div>
                </div>
                {/if}


                <div class="form-group">
                    <label for="c-classes_type" class="control-label col-xs-12 col-sm-2">班级类型:</label>
                    <div class="col-xs-12 col-sm-8" id="div-classes_type">
                        <select name="row[classes_type]" id="c-classes_type" class="form-control" data-rule="">
                        </select>
                    </div>
                </div>

                {if $admin['group_id']!=8}
                <div class="form-group">
                    <label for="c-nianji" class="control-label col-xs-12 col-sm-2">年级:</label>
                    <div class="col-xs-12 col-sm-8" id="div-nianji">
                        <input type="text" id="c-nianji" name="row[nianji]" value="{$row.nianji}" class="form-control"
                               title="name" placeholder="" data-rule="" data-tip="年级"/>
                    </div>
                </div>
                {/if}
                
                {if $admin['group_id']==3}
                <div class="form-group">
                    <label for="c-addresslv_id_tree" class="control-label col-xs-12 col-sm-2">学院:</label>
                    <div class="col-xs-12 col-sm-8" id="div-school_id">
                        <select name="row[addresslv_id_tree]" id="c-addresslv_id_tree" class="form-control">
                        </select>
                    </div>
                </div>
                {/if}{if $admin['group_id']==8}
                <div class="form-group">
                    <label for="c-community_id" class="control-label col-xs-12 col-sm-2">学校:</label>
                    <div class="col-xs-12 col-sm-8" id="div-community_id">
                        <select name="row[community_id]" id="c-community_id" class="form-control">
                        </select>
                    </div>
                </div>
                {/if}
                
                {if $admin['group_id']!=8}
                <div class="form-group">
                    <label for="c-xueqi" class="control-label col-xs-12 col-sm-2">学期:</label>
                    <div class="col-xs-12 col-sm-8" id="div-xueqi">
                        <select id="c-xueqi" name="row[xueqi]" class="form-control" data-tip="学期">
                            <option value="春季" {if $row["xueqi"] == "春季"}selected{/if}>春季</option>
                            <option value="秋季" {if $row["xueqi"] == "秋季"}selected{/if}>秋季</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group" {if $row["addresslv_id"] == 2}style="display:none"{/if}>
                    <label for="c-qishu" class="control-label col-xs-12 col-sm-2">期数:</label>
                    <div class="col-xs-12 col-sm-8" id="div-qishu">
                        <input type="text" id="c-qishu" name="row[qishu]" value="{$row.qishu}" class="form-control"
                               title="qishu" placeholder="" data-rule="" data-tip="期数"/>
                    </div>
                </div>
                {/if}
                
                {if $admin['group_id']==3}
                <div class="form-group">
                    <label for="c-subject_lv" class="control-label col-xs-12 col-sm-2">课程类别:</label>
                    <div class="col-xs-12 col-sm-8" id="div-subject_lv">
                        <select id="c-subject_lv" name="row[subject_lv]" value="{$row.subject_lv}" class="form-control"
                                title="kcmc" placeholder="" data-rule="" data-tip="课程名称"></select>
                    </div>
                </div>
                {/if}{if $admin['group_id']==8}
            <div class="form-group" id="div-class_type_css">
                <label for="c-class_type" class="control-label col-xs-12 col-sm-2">课程类型:</label>
                <div class="col-xs-12 col-sm-8" id="div-class_type">
                    <select name="row[class_type]" id="c-class_type" class="form-control" data-rule=""
                            multiple></select>
                </div>
            </div>
            {/if}

            <div class="form-group">
                <label for="c-name" class="control-label col-xs-12 col-sm-2">班级名称:</label>
                <div class="col-xs-12 col-sm-8" id="div-name">
                    <input type="text" id="c-name" name="row[name]" value="{$row.name}" class="form-control"
                           title="name" placeholder="" data-rule="" data-tip="班级名称"/>
                </div>
            </div>

            <div class="form-group">
                <label for="c-cover" class="control-label col-xs-12 col-sm-2">班级封面:</label>
                <div class="col-xs-12 col-sm-8" id="div-cover">
                    <div class="form-inline">
                        <input id="c-cover" class="form-control" size="38" name="row[cover]" type="text"
                               value="{$row.cover}" data-tip="">
                        <span><button type="button" id="plupload-cover" class="btn btn-danger plupload"
                                      data-input-id="c-cover" data-mimetype="image/*" data-multiple="false"
                                      data-preview-id="p-cover"><i
                                class="fa fa-upload"></i> {:__('Upload')}</button></span>
                        <span><button type="button" id="ai-generate-cover" class="btn btn-success ai-generate"
                                      data-input-id="c-cover" data-say="为这个班级生成一张有创意的封面图片，风格现代简洁，色彩温暖"><i
                                class="fa fa-magic"></i> AI自动</button></span>
                        <ul class="row list-inline plupload-preview" id="p-cover"></ul>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="c-intro" class="control-label col-xs-12 col-sm-2">班级介绍:</label>
                <div class="col-xs-12 col-sm-8" id="div-intro">
                        <textarea name="row[intro]" id="c-intro" class="form-control editor" data-rule="" rows="5"
                                  data-tip="班级介绍">{$row.intro}</textarea>
                </div>
            </div>
            <div class="form-group">
                <label for="c-num" class="control-label col-xs-12 col-sm-2">学费:</label>
                <div class="col-xs-12 col-sm-8" id="div-price">
                    <input type="number" id="c-price" name="row[price]" value="{$row['price']}" class="form-control" title="price"
                           data-rule="number" step="0.01" placeholder="请输入金额，支持两位小数" data-tip="学费金额"/>
                </div>
            </div>
        </div>
        <div class="tab-pane fade active in" id="tabs_1">
            <div id="app">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">班长:</label>
                    <div class="col-xs-12 col-sm-8" id="div-student_id">
                        <leader_box :type="''" :left-options="leftOptions" :right-options="rightOptions"></leader_box>
                    </div>
                </div>

                <!--div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">管理员:</label>
                    <div class="col-xs-12 col-sm-8" id="div-adminid" >
                        <manager-box :type="'single'" :left-options="leftOptions" :right-options="rightOptions"></manager-box>
                    </div>
                </div-->
            </div>
            <div class="form-group" style="display: none">
                <label for="c-manage_creator_id" class="control-label col-xs-12 col-sm-2">管理岗创建者:</label>
                <div class="col-xs-12 col-sm-8" id="div-manage_creator_id">
                    <input type="number" id="c-manage_creator_id" name="row[manage_creator_id]" value="0"
                           class="form-control" title="manage_creator_id" data-rule="integer"
                           placeholder="只能填入整数" data-tip="管理岗创建者"/>
                </div>
            </div>

            <div class="form-group">
                <label for="c-num" class="control-label col-xs-12 col-sm-2">班级人数:</label>
                <div class="col-xs-12 col-sm-8" id="div-num">
                    <input type="number" id="c-num" name="row[num]" value="50" class="form-control" title="num"
                           data-rule="integer" placeholder="只能填入整数" data-tip="班级人数"/>
                </div>
            </div>


        </div>
        <div class="tab-pane fade active in" id="tabs_2">

            <div class="form-group">
                <label for="c-is_apply_to_join" class="control-label col-xs-12 col-sm-2">是否开放申请加入:</label>
                <div class="col-xs-12 col-sm-8" id="div-is_apply_to_join">
                    <!-- 修改为开关组件22 -->
                    <input id="c-is_apply_to_join" name="row[is_apply_to_join]" type="hidden" value="{$row['is_apply_to_join']}">
                    <a href="javascript:;" data-toggle="switcher" class="btn-switcher {if $row.is_apply_to_join=='1'}active{else}text-gray{/if}" data-input-id="c-is_apply_to_join" data-yes="1" data-no="0">
                        <i class="fa fa-toggle-on fa-2x {if $row['is_apply_to_join']!='1'}fa-flip-horizontal{/if}"></i>
                    </a>
                    <div data-favisible="switch=1" class="p-3">是</div>
                    <div data-favisible="switch=0" class="p-3">否</div>
                </div>
            </div>

            <div class="form-group" style="display: none">
                <label for="c-creator_id" class="control-label col-xs-12 col-sm-2">创建者:</label>
                <div class="col-xs-12 col-sm-8" id="div-creator_id">
                    <input type="number" id="c-creator_id" name="row[creator_id]" value="0" class="form-control"
                           title="creator_id" data-rule="integer" placeholder="只能填入整数" data-tip="创建者"/>
                </div>
            </div>

            <div class="form-group">
                <label for="c-show_in_team" class="control-label col-xs-12 col-sm-2">是否在班级团队列表中显示:</label>
                <div class="col-xs-12 col-sm-8" id="div-show_in_team">
                    <input id="c-show_in_team" name="row[show_in_team]" type="hidden" value="{$row['show_in_team']}">
                    <a href="javascript:;" data-toggle="switcher" class="btn-switcher {if $row.show_in_team=='1'}active{else}text-gray{/if}" data-input-id="c-show_in_team" data-yes="1" data-no="0">
                        <i class="fa fa-toggle-on  fa-2x {if $row.show_in_team!='1'}fa-flip-horizontal{/if}"></i>
                    </a>
                    <div data-favisible="switch=1" class="p-3">是</div>
                    <div data-favisible="switch=2" class="p-3">否</div>
                </div>
            </div>

            <div class="form-group">
                <label for="c-is_check" class="control-label col-xs-12 col-sm-2">加入班级是否需要审核:</label>
                <div class="col-xs-12 col-sm-8" id="div-is_check">
                    <input id="c-is_check" name="row[is_check]" type="hidden" value="{$row['is_check']}">
                    <a href="javascript:;" data-toggle="switcher" class="btn-switcher {if $row.is_check}active{else}text-gray{/if}" data-input-id="c-is_check" data-yes="1" data-no="0">
                        <i class="fa fa-toggle-on  fa-2x {if $row.is_check!='1'}fa-flip-horizontal{/if}"></i>
                    </a>
                    <div data-favisible="switch=1" class="p-3">是</div>
                    <div data-favisible="switch=2" class="p-3">否</div>
                </div>
            </div>

            <div class="form-group">
                <label for="c-allow_see" class="control-label col-xs-12 col-sm-2">允许查看班级属性:</label>
                <div class="col-xs-12 col-sm-8" id="div-allow_see">
                    <input id="c-allow_see" name="row[allow_see]" type="hidden" value="{$row['allow_see']}">
                    <a href="javascript:;" data-toggle="switcher" class='btn-switcher {if $row.allow_see=="1"}active{else}text-gray{/if}' data-input-id="c-allow_see" data-yes="1" data-no="0" >
                        <i class="fa fa-toggle-on fa-2x {if $row.allow_see!='1'}fa-flip-horizontal{/if}"></i>
                    </a>
                    <div data-favisible="switch=1" class="p-3">允许</div>
                    <div data-favisible="switch=2" class="p-3">不允许</div>
                </div>
            </div>

            <div class="form-group">
                <label for="c-status" class="control-label col-xs-12 col-sm-2">状态:</label>
                <div class="col-xs-12 col-sm-8" id="div-status">
                    <input id="c-status" name="row[status]" type="hidden" value="{$row.status}">
                    <a href="javascript:;" data-toggle="switcher" class="btn-switcher {if $row.status=='1'}active{else}text-gray{/if}" data-input-id="c-status" data-yes="1" data-no="0">
                        <i class="fa fa-toggle-on fa-2x {if $row.status!='1'}fa-flip-horizontal{/if}"></i>
                    </a>
                    <div data-favisible="switch=1" class="p-3">已开户</div>
                    <div data-favisible="switch=2" class="p-3">不允许</div>
                </div>
            </div>


            <input type="hidden" id="province" id="c-cityarea_id" name="row[province_id]"
                   value="{$row.province_id}">
            <input type="hidden" id="c-city_id" name="row[city_id]" value="{$row.city_id}">
            <input type="hidden" id="c-district_id" name="row[district_id]" value="{$row.district_id}">

            <div class="form-group" style="display: none">
                <label class="control-label col-xs-12 col-sm-2">创建者账号类型:</label>
                <div class="col-xs-12 col-sm-8" id="div-creator_type">
                    <label for="row[creator_type]-1"><input id="row[creator_type]-1" name="row[creator_type]"
                                                            type="radio" value="1" {if $row["creator_type"]=="1"}checked{/if}
                        /> 老师或管理员</label>

                    <label for="row[creator_type]-2"><input id="row[creator_type]-2" name="row[creator_type]"
                                                            type="radio" value="2" {if $row["creator_type"]=="2"}checked{/if}
                        /> 组织账号</label>
                </div>
            </div>
            <div class="form-group layer-footer">
                <label class="control-label col-xs-12 col-sm-2"></label>
                <div class="col-xs-12 col-sm-8">
                    <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
                    <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                </div>
            </div>
        </div>

    </div>
    </div>
</form>
