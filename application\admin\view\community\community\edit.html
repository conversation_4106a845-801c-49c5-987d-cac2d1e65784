<form id="edit-form" class="form-horizontal" style="position:relative;background: #fff" role="form" method="POST" action="">
    <input type="hidden" id="row" name="data" value='<?php echo json_encode($row);?>' />
    <div class="panel-body">
        <div id="myTabContent" class="tab-content" >
            <div class="tab-pane fade active in" id="tabs_0">
                <div class="form-group">
                    <label for="c-name" class="control-label col-xs-12 col-sm-2">社区办学名称:</label>
                    <div class="col-xs-12 col-sm-8" id="div-name" >
                        <input type="text" id="c-name" name="row[name]" value="{$row.name}" class="form-control" title="name"  placeholder="输入社区办学机构名称" data-rule=""  data-tip="社区名称"  />
                    </div>
               </div>

               <div class="form-group">
                    <label for="c-province_id" class="control-label col-xs-12 col-sm-2">行政区划:</label>
                    <div class="col-xs-12 col-sm-8" id="div-province_id" >
                    <div style="margin-top:-2px;margin-left: -2px;">
                        <select id="c-cityarea_id" class="form-control liandong"  name="row[province_id]" data-source="/cityarea/cityarea/getAreas?level=0&parent_id=0&id={$row.province_id}"
                                type="text" value="{$row.province_id}">

                        </select>
                        <select id="city" class="form-control liandong"  name="row[city_id]" data-source="/cityarea/cityarea/getAreas?level=1&parent_id={$row.province_id}&id={$row.city_id}"
                                type="text" value="{$row.city_id}">

                        </select>
                        <select id="county" class="form-control liandong"  name="row[district_id]" data-source="/cityarea/cityarea/getAreas?level=2&parent_id={$row.city_id}&id={$row.district_id}"
                                type="text" value="{$row.district_id}">

                        </select>
                    </div>                        </div>
               </div>

               <div class="form-group">
                    <label for="c-address" class="control-label col-xs-12 col-sm-2">地址:</label>
                    <div class="col-xs-12 col-sm-8" id="div-address" >
                        <input type="text" id="c-address" name="row[address]" value="{$row.address}" class="form-control" title="address"  placeholder="" data-rule=""  data-tip="地址"  />
                    </div>
               </div>

               <div class="form-group">
                    <label for="c-school_logo" class="control-label col-xs-12 col-sm-2">社区学校Logo:</label>
                    <div class="col-xs-12 col-sm-8" id="div-school_logo" >
                        <div class="form-inline">
                            <input id="c-school_logo" class="form-control" size="39" name="row[school_logo]" type="text" value="{$row.school_logo}" data-tip="">
                            <span><button type="button" id="plupload-school_logo" class="btn btn-danger plupload" data-input-id="c-school_logo" data-mimetype="image/*" data-multiple="false" data-preview-id="p-school_logo"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                            <span><button type="button" id="fachoose-school_logo" class="btn btn-primary fachoose" data-input-id="c-school_logo" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                            <ul class="row list-inline plupload-preview" id="p-school_logo"></ul>
                        </div>
                    </div>
               </div>

               <div class="form-group">
                    <label for="c-thumbnail" class="control-label col-xs-12 col-sm-2">主校封面图:</label>
                    <div class="col-xs-12 col-sm-8" id="div-thumbnail" >
                        <div class="form-inline">
                            <input id="c-thumbnail" class="form-control" size="39" name="row[thumbnail]" type="text" value="{$row.thumbnail}" data-tip="">
                            <span><button type="button" id="plupload-thumbnail" class="btn btn-danger plupload" data-input-id="c-thumbnail" data-mimetype="image/*" data-multiple="false" data-preview-id="p-thumbnail"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                            <span><button type="button" id="fachoose-thumbnail" class="btn btn-primary fachoose" data-input-id="c-thumbnail" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                            <ul class="row list-inline plupload-preview" id="p-thumbnail"></ul>
                        </div>
                    </div>
               </div>

               <div class="form-group">
                    <label for="c-intro" class="control-label col-xs-12 col-sm-2">社区学校简介:</label>
                    <div class="col-xs-12 col-sm-8" id="div-intro" >
                        <textarea name="row[intro]" id="c-intro" class="form-control editor" data-rule="" rows="5" data-tip="社区学校简介" >{$row.intro}</textarea>
                    </div>
               </div>

               <div class="form-group">
                    <label for="c-feature" class="control-label col-xs-12 col-sm-2">社区学校特色:</label>
                    <div class="col-xs-12 col-sm-8" id="div-feature" >
                        <textarea name="row[feature]" id="c-feature" class="form-control" data-rule="" rows="5" data-tip="社区学校特色" >{$row.feature}</textarea>
                    </div>
               </div>

               <div class="form-group">
                    <label for="c-is_list" class="control-label col-xs-12 col-sm-2">是否在“老年大学”列表显示:</label>
                    <div class="col-xs-12 col-sm-8" id="div-is_list" >
                        <label for="row[is_list]-1"><input id="row[is_list]-1" name="row[is_list]" type="radio" value="1" data-tip="是否在“老年大学”列表显示" {if $row["is_list"]=="1"}checked{/if} /> 是</label>

                        <label for="row[is_list]-0"><input id="row[is_list]-0" name="row[is_list]" type="radio" value="0" data-tip="是否在“老年大学”列表显示" {if $row["is_list"]=="0"}checked{/if} /> 否</label>
                    </div>
               </div>

               <div class="form-group">
                    <label for="c-status" class="control-label col-xs-12 col-sm-2">状态:</label>
                    <div class="col-xs-12 col-sm-8" id="div-status" >                    
                        <input id="c-status" name="row[status]" type="hidden" value="{$row.status}">
                        <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-status" data-yes="1" data-no="0">
                            <i class="fa fa-toggle-on text-success fa-flip-horizontal fa-2x"></i>
                        </a>
                    </div>
               </div>
                <div class="form-group layer-footer">
                    <label class="control-label col-xs-12 col-sm-2"></label>
                    <div class="col-xs-12 col-sm-8">
                        <button type="submit" class="btn btn-success btn-embossed" id="submit-btn">{:__('OK')}</button>
                        <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                    </div>
                </div>
            </div>
            
        </div>
    </div>
</form>


