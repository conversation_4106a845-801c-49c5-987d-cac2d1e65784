{extend name="common/base" /}

{block name="content"}
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <ul class="nav nav-tabs" data-field="status">
            <li class="active"><a href="#one" data-toggle="tab">AI图片生成</a></li>
            <li><a href="#two" data-toggle="tab">生成历史</a></li>
            <li><a href="#three" data-toggle="tab">系统设置</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div class="tab-content">
            <!-- AI图片生成 -->
            <div class="tab-pane fade active in" id="one">
                <div class="row">
                    <div class="col-md-6">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">单张图片生成</h4>
                            </div>
                            <div class="panel-body">
                                <form id="single-generate-form">
                                    <div class="form-group">
                                        <label>提示词</label>
                                        <textarea class="form-control" name="prompt" rows="3" placeholder="请输入图片描述..."></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label>图片尺寸</label>
                                        <select class="form-control" name="size">
                                            <option value="1024*1024" selected>正方形 (1024*1024)</option>
                                            <option value="720*1280">竖图 (720*1280)</option>
                                            <option value="1280*720">横图 (1280*720)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>图片风格</label>
                                        <select class="form-control" name="style">
                                            <option value="default">默认</option>
                                            <option value="modern">现代风格</option>
                                            <option value="cartoon">卡通风格</option>
                                            <option value="realistic">写实风格</option>
                                            <option value="artistic">艺术风格</option>
                                            <option value="professional">专业风格</option>
                                            <option value="educational">教育主题</option>
                                        </select>
                                    </div>
                                    <button type="button" class="btn btn-primary" id="single-generate-btn">
                                        <i class="fa fa-magic"></i> 生成图片
                                    </button>
                                </form>
                                
                                <div id="single-result" class="mt-3" style="display:none;">
                                    <h5>生成结果：</h5>
                                    <div id="single-preview"></div>
                                    <div class="mt-2">
                                        <input type="text" class="form-control" id="single-url" readonly placeholder="图片URL">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">批量图片生成</h4>
                            </div>
                            <div class="panel-body">
                                <form id="batch-generate-form">
                                    <div class="form-group">
                                        <label>提示词列表 <small class="text-muted">(每行一个)</small></label>
                                        <textarea class="form-control" name="prompts" rows="5" placeholder="请输入多个图片描述，每行一个..."></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label>图片尺寸</label>
                                        <select class="form-control" name="batch_size">
                                            <option value="1024*1024" selected>正方形 (1024*1024)</option>
                                            <option value="720*1280">竖图 (720*1280)</option>
                                            <option value="1280*720">横图 (1280*720)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>图片风格</label>
                                        <select class="form-control" name="batch_style">
                                            <option value="default">默认</option>
                                            <option value="modern">现代风格</option>
                                            <option value="cartoon">卡通风格</option>
                                            <option value="realistic">写实风格</option>
                                            <option value="artistic">艺术风格</option>
                                            <option value="professional">专业风格</option>
                                            <option value="educational">教育主题</option>
                                        </select>
                                    </div>
                                    <button type="button" class="btn btn-success" id="batch-generate-btn">
                                        <i class="fa fa-magic"></i> 批量生成
                                    </button>
                                </form>
                                
                                <div id="batch-result" class="mt-3" style="display:none;">
                                    <h5>批量生成结果：</h5>
                                    <div id="batch-preview"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 生成历史 -->
            <div class="tab-pane fade" id="two">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">AI图片生成历史</h4>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table id="history-table" class="table table-striped table-bordered">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>提示词</th>
                                        <th>尺寸</th>
                                        <th>风格</th>
                                        <th>预览</th>
                                        <th>生成时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="7" class="text-center text-muted">暂无历史记录</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 系统设置 -->
            <div class="tab-pane fade" id="three">
                <div class="row">
                    <div class="col-md-6">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">AI服务配置</h4>
                            </div>
                            <div class="panel-body">
                                <div class="form-group">
                                    <label>当前AI服务商</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="current-provider" readonly>
                                        <div class="input-group-addon">
                                            <span id="provider-status">
                                                <i class="fa fa-circle text-muted"></i>
                                            </span>
                                        </div>
                                    </div>
                                    <small class="text-muted">当前使用的AI图像生成服务</small>
                                </div>
                                
                                <div class="form-group">
                                    <label>API密钥配置</label>
                                    <div class="alert alert-info">
                                        <h6><i class="fa fa-info-circle"></i> 环境变量配置</h6>
                                        <p><strong>阿里千问:</strong> 设置环境变量 <code>AI_QIANWEN_KEY</code></p>
                                        <p><strong>OpenAI:</strong> 设置环境变量 <code>AI_OPENAI_KEY</code></p>
                                        <p class="mb-0">或直接在配置文件 <code>application/extra/ai.php</code> 中配置</p>
                                    </div>
                                </div>
                                
                                <button type="button" class="btn btn-info" id="test-connection-btn">
                                    <i class="fa fa-plug"></i> 测试连接
                                </button>
                                <button type="button" class="btn btn-default" onclick="refreshProviderInfo()">
                                    <i class="fa fa-refresh"></i> 刷新状态
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">系统状态</h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <div class="info-box bg-green">
                                            <span class="info-box-icon"><i class="fa fa-image"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">今日生成</span>
                                                <span class="info-box-number" id="today-count">0</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="info-box bg-blue">
                                            <span class="info-box-icon"><i class="fa fa-history"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">总计生成</span>
                                                <span class="info-box-number" id="total-count">0</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <h5>存储信息</h5>
                                    <p><strong>存储路径：</strong> /uploads/ai_images/</p>
                                    <p><strong>支持格式：</strong> PNG, JPG, JPEG, WEBP</p>
                                    <p><strong>最大文件：</strong> 10MB</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
$(document).ready(function() {
    // 页面加载时获取服务商信息
    loadProviderInfo();
    
    // 单张图片生成
    $('#single-generate-btn').click(function() {
        var $btn = $(this);
        var prompt = $('textarea[name="prompt"]').val();
        var size = $('select[name="size"]').val();
        var style = $('select[name="style"]').val();
        
        if (!prompt.trim()) {
            Toastr.error('请输入提示词');
            return;
        }
        
        var originalText = $btn.html();
        $btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 生成中...');
        
        $.ajax({
            url: 'ai/generateImage',
            type: 'POST',
            data: {
                prompt: prompt,
                size: size,
                style: style
            },
            dataType: 'json',
            success: function(response) {
                if (response.code === 1) {
                    $('#single-result').show();
                    $('#single-preview').html('<img src="' + response.data.image_url + '" class="img-responsive" style="max-width:300px;">');
                    $('#single-url').val(response.data.image_url);
                    Toastr.success('图片生成成功！');
                } else {
                    Toastr.error(response.msg || '生成失败');
                }
            },
            error: function() {
                Toastr.error('请求失败，请检查网络连接');
            },
            complete: function() {
                $btn.prop('disabled', false).html(originalText);
            }
        });
    });
    
    // 批量图片生成
    $('#batch-generate-btn').click(function() {
        var $btn = $(this);
        var promptsText = $('textarea[name="prompts"]').val();
        var size = $('select[name="batch_size"]').val();
        var style = $('select[name="batch_style"]').val();
        
        if (!promptsText.trim()) {
            Toastr.error('请输入提示词列表');
            return;
        }
        
        var prompts = promptsText.split('\n').filter(function(line) {
            return line.trim() !== '';
        });
        
        if (prompts.length === 0) {
            Toastr.error('请输入有效的提示词');
            return;
        }
        
        var originalText = $btn.html();
        $btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 批量生成中...');
        
        $.ajax({
            url: 'ai/batchGenerate',
            type: 'POST',
            data: {
                prompts: prompts,
                size: size,
                style: style
            },
            dataType: 'json',
            success: function(response) {
                if (response.code === 1) {
                    var data = response.data;
                    $('#batch-result').show();
                    
                    var html = '<div class="row">';
                    data.results.forEach(function(item) {
                        html += '<div class="col-md-4 mb-3">';
                        html += '<div class="thumbnail">';
                        html += '<img src="' + item.image_url + '" class="img-responsive">';
                        html += '<div class="caption">';
                        html += '<p><small>' + item.prompt + '</small></p>';
                        html += '<input type="text" class="form-control input-sm" value="' + item.image_url + '" readonly>';
                        html += '</div>';
                        html += '</div>';
                        html += '</div>';
                    });
                    html += '</div>';
                    
                    $('#batch-preview').html(html);
                    Toastr.success('批量生成完成！成功：' + data.success_count + '，失败：' + data.error_count);
                } else {
                    Toastr.error(response.msg || '批量生成失败');
                }
            },
            error: function() {
                Toastr.error('请求失败，请检查网络连接');
            },
            complete: function() {
                $btn.prop('disabled', false).html(originalText);
            }
        });
    });
    
    // 测试连接
    $('#test-connection-btn').click(function() {
        var $btn = $(this);
        var originalText = $btn.html();
        $btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 测试中...');
        
        $.ajax({
            url: 'ai/testConnection',
            type: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.code === 1) {
                    Toastr.success('连接测试成功！');
                } else {
                    Toastr.error(response.msg || '连接测试失败');
                }
            },
            error: function() {
                Toastr.error('连接测试失败');
            },
            complete: function() {
                $btn.prop('disabled', false).html(originalText);
            }
        });
    });
});

// 加载服务商信息
function loadProviderInfo() {
    $.ajax({
        url: 'ai/getProviderInfo',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.code === 1) {
                var data = response.data;
                $('#current-provider').val(data.provider);
                $('#provider-status').html('<i class="fa fa-circle text-' + (data.status ? 'success' : 'danger') + '"></i>');
            } else {
                Toastr.error(response.msg || '获取服务商信息失败');
            }
        },
        error: function() {
            Toastr.error('获取服务商信息失败');
        }
    });
}

// 刷新服务商状态
function refreshProviderInfo() {
    loadProviderInfo();
}
</script>
{/block}
