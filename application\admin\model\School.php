<?php

namespace app\admin\model;
use think\Model;
use fast\Tree;
use think\Db;

class School extends Model{
    // 表名
    public $name = 'school';
    public $prefix_new = 'eb_';
    public $falseDelete = true;
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    public $data;
    public $modelValidate = true;

    public function getArea_idTextAttr($value, $data=array())
    {
        if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("cityarea")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }
    public function getSchool_idTextAttr($value, $data=array())
    {
        if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Db::name('eb_school')->find(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }
    protected function setArea_idAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getDistrict_idTextAttr($value, $data)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("cityarea")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    protected function setDistrict_idAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }


    public function getCity_idTextAttr($value, $data)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("cityarea")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    protected function setCity_idAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

}