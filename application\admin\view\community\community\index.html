<div class="panel panel-default panel-intro">
    {:build_heading()}
    
    <!-- 功能板块操作提示栏 -->
    <div class="panel-body" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin-bottom: 0;">
        <div class="row">
            <div class="col-md-12">
                <h4 style="margin-top: 0; color: white;"><i class="fa fa-info-circle"></i> 社区管理模块操作指引</h4>
                <div class="progress-steps">
                    <div class="step-container">
                        <div class="step active" data-step="1">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h5>社区办学机构</h5>
                                <p>在社区列表里，先新建或完善社区办学机构名称</p>
                                <div class="step-action">
                                    <button class="btn btn-xs btn-success" onclick="goToNextStep(2, '/community/project/setcourse')" style="margin-top: 8px;">
                                        <i class="fa fa-arrow-right"></i> 如果已完善，点击到下一步
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="step" data-step="2">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h5>项目课程预置</h5>
                                <p>在社区项目课程管理里，先新建或完善项目课程预置</p>
                                <div class="step-action">
                                    <button class="btn btn-xs btn-success" onclick="goToNextStep(3, '/community/project/publicwelfare')" style="margin-top: 8px;">
                                        <i class="fa fa-arrow-right"></i> 如果已完善，点击到下一步
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="step" data-step="3">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h5>项目管理配置</h5>
                                <p>新建或管理项目，配置课程代码、社区代码、时区代码和时间关联配置</p>
                                <div class="step-action">
                                    <button class="btn btn-xs btn-success" onclick="goToNextStep(4, '/course/schedule')" style="margin-top: 8px;">
                                        <i class="fa fa-arrow-right"></i> 如果已完善，点击到下一步
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="step" data-step="4">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h5>查看排课表格</h5>
                                <p>在公益课程短期里查看系统自动生成的排课表格</p>
                                <div class="step-action">
                                    <button class="btn btn-xs btn-info" onclick="window.open('/admin/coursetable/autoplan/index', '_blank')" style="margin-top: 8px;">
                                        <i class="fa fa-eye"></i> 查看排课表格
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="current-step-info">
                    <span class="label label-warning">当前步骤：第一步 - 社区办学机构管理</span>
                    <a href="javascript:;" class="btn btn-sm btn-info pull-right" onclick="toggleStepsGuide()">收起/展开指引</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加样式 -->
    <style>
    .progress-steps {
        margin: 20px 0;
    }
    .step-container {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        position: relative;
    }
    .step {
        flex: 1;
        text-align: center;
        position: relative;
        padding: 0 10px;
    }
    .step:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 20px;
        right: -50%;
        width: 100%;
        height: 2px;
        background: rgba(255,255,255,0.3);
        z-index: 1;
    }
    .step.active .step-number {
        background: #28a745;
        color: white;
        box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
    }
    .step.completed .step-number {
        background: #17a2b8;
        color: white;
    }
    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: rgba(255,255,255,0.3);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 10px;
        font-weight: bold;
        font-size: 16px;
        position: relative;
        z-index: 2;
        transition: all 0.3s ease;
    }
    .step-content h5 {
        color: white;
        margin-bottom: 5px;
        font-size: 14px;
    }
    .step-content p {
        color: rgba(255,255,255,0.9);
        font-size: 12px;
        margin: 0;
        line-height: 1.4;
    }
    .step-action {
        margin-top: 8px;
    }
    .step-action .btn {
        font-size: 11px;
        padding: 4px 8px;
        border-radius: 3px;
        transition: all 0.3s ease;
    }
    .step-action .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    .current-step-info {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid rgba(255,255,255,0.2);
    }
    .steps-guide-collapsed .progress-steps {
        display: none;
    }
    </style>
    
    <!-- 添加JavaScript -->
    <script>
    function toggleStepsGuide() {
        var panel = document.querySelector('.panel-body');
        panel.classList.toggle('steps-guide-collapsed');
    }
    
    // 跳转到下一步的函数
    function goToNextStep(stepNumber, url) {
        // 更新当前步骤状态
        updateCurrentStep(stepNumber);
        
        // 显示成功提示
        layer.msg('正在跳转到第' + stepNumber + '步...', {
            icon: 1,
            time: 1500
        }, function(){
            // 跳转到指定页面
            if (url) {
                window.location.href = url;
            }
        });
    }
    
    // 根据当前页面更新步骤状态
    function updateCurrentStep(stepNumber) {
        document.querySelectorAll('.step').forEach(function(step, index) {
            step.classList.remove('active', 'completed');
            if (index + 1 < stepNumber) {
                step.classList.add('completed');
            } else if (index + 1 === stepNumber) {
                step.classList.add('active');
            }
        });
        
        var stepNames = [
            '第一步 - 社区办学机构管理',
            '第二步 - 项目课程预置管理', 
            '第三步 - 项目管理配置',
            '第四步 - 查看排课表格'
        ];
        
        document.querySelector('.current-step-info .label').textContent = '当前步骤：' + stepNames[stepNumber - 1];
    }
    
    // 页面加载时设置当前步骤（社区列表页面为第一步）
    document.addEventListener('DOMContentLoaded', function() {
        updateCurrentStep(1);
    });
    </script>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('community/community/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('community/community/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('community/community/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>

                        <div class="dropdown btn-group {:$auth->check('community/community/multi')?'':'hide'}">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> {:__('More')}</a>
                            <ul class="dropdown-menu text-left" role="menu">
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=normal"><i class="fa fa-eye"></i> {:__('Set to normal')}</a></li>
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=hidden"><i class="fa fa-eye-slash"></i> {:__('Set to hidden')}</a></li>
                            </ul>
                        </div>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover"
                           data-operate-edit="{:$auth->check('community/community/edit')}"
                           data-operate-del="{:$auth->check('community/community/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
