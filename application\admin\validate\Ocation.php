<?php

namespace app\admin\validate;

use think\Validate;

class Ocation extends Validate
{

    /**
     * 验证规则
     */
    protected $rule = [
        'name' => 'require|regex:.{8,40}|unique:admin,name',
    ];

    protected function checkDistrictId($value, $rule, $data)
    {
        if ($data['city_id']== '0') {
            return true;
        }
        if($value==""||$value=="-1"){
            return false;
        }
        return true;
    }

    /**
     * 提示消息
     */
    protected $message = [];

    /**
     * 字段描述
     */
    protected $field = [];

    /**
     * 验证场景
     */
    protected $scene = [
        'add' => [
            'name' => 'require',
            'city_id' => 'require',
            'district_id' => 'checkDistrictId',
            'name' => 'require|regex:.{8,40}|unique:school,name',
            'address' => 'require',
        ],
        'edit' => [
            'name' => 'require',
            'city_id' => 'require',
            'district_id' => 'checkDistrictId',
            'name' => 'require|regex:.{8,40}',
            //'address' => 'require',
        ]
    ];

    public function __construct(array $rules = [], $message = [], $field = [])
    {
        $this->field = [
            'name' => "教学点名称",
        ];
        $this->message = array_merge($this->message, [
            'name.regex' => '学校名称长度出错',
            'name.require' => '省份必选',
        ]);
        parent::__construct($rules, $message, $field);
    }

}
