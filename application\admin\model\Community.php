<?php

namespace app\admin\model;
use think\Model;
use fast\Tree;
use think\Db;
class Community extends Model
{
    // 表名
    public $name = 'community';
    public $prefix_new = 'fa_';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    public $data;


    public function getIs_listTextAttr($value, $data=array())
    {
        $s=array();
        $content=array(
            "1"=>"是",
            "0"=>"否",
            );
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            if(isset($content[$nn[$i]])) $s[]= $content[$nn[$i]];
        }
        return join(",",$s);
    }

    public function getCommunity_idTextAttr($value, $data=array())
    {
        if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Db::name("community_code_config")->find(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

}