<?php

namespace app\admin\model;
use think\Model;
use fast\Tree;
use traits\model\SoftDelete;

class Article extends Model
{
    // 表名
    public $name = 'article';

    // 允许写入的字段
    protected $field = ['id', 'title', 'content', 'thumb', 'image', 'video', 'city_id', 'school_id', 'status', 'createtime', 'update_time', 'create_time', 'delete_time'];

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    public $data;

    use SoftDelete;
    protected $auto = ['delete_time'];

    protected function setDeletetimeAttr($value)
    {
        if (is_numeric($value)) {
            return date('Y-m-d H:i:s', $value);
        }
        return $value;
    }

    public function getCityidTextAttr($value, $data)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("cityarea")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    protected function setCityidAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

    public function getSchoolidTextAttr($value, $data)
    {
                if($value) {
            $nn=preg_split("/,/",$value);
        }else{
            $nn=array();
        }
        $s=array();
        for($i=0;$i<count($nn);$i++) {
            if($nn[$i]=="") continue;
            $r = Model("school")->get(array("id" => $nn[$i]));
            if($r) $s[]= $r['name'];
        }
        return join(",",$s);
    }

    protected function setSchoolidAttr($value)
    {
        return $value&&is_array($value)?join(",",$value):$value;
    }

}