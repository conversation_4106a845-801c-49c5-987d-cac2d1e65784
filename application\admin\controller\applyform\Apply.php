<?php

namespace app\admin\controller\applyform;

use app\common\controller\Backend;
use fast\Tree;
use think\Db;

/**
 * 
 *
 * @icon fa fa-file
 */
class Apply extends Backend
{
    
    /**
     * Apply模型对象
     */
    protected $model = null;
    protected $noNeedRight=["lists","status","getSubjectNames","studentList"];
    protected $noNeedLogin=["lists","status","getSubjectNames","studentList"];


    public function _initialize()
    {
        parent::_initialize();
        $this->shopid=false;
        $this->model = model('Apply');
        $this->modelValidate = true;
    }
    
    /**
     * 获取报名学员列表
     * @return \think\response\Json
     */
    public function studentList()
    {
        try {
            $applyId = $this->request->get('apply_id/d', 0);
            if (!$applyId) {
                return json(['code' => 0, 'msg' => '缺少报名ID']);
            }
            
            // 查询报名信息
            $apply = $this->model->where('id', $applyId)->find();
            if (!$apply) {
                return json(['code' => 0, 'msg' => '报名信息不存在']);
            }

            // 查询报名学员数据
            $applyFormItemModel = new \app\admin\model\ApplyFormItem();
            $applyFormItems = $applyFormItemModel
                ->alias('a')
                ->join('fa_order o', 'a.order_id = o.id', 'LEFT')
                ->join('fa_user u', 'a.user_id = u.id', 'LEFT')
                ->field('a.id,a.intro as course_name,a.create_time,o.amount,o.payment_status,o.pay_time,o.order_sn,u.username,u.mobile,u.gender,u.avatar')
                ->select();

            // 检查查询结果
            if (empty($applyFormItems)) {
                return json(['code' => 0, 'msg' => '暂无报名学员数据']);
            }

            // 处理结果
            $result = [];
            foreach ($applyFormItems as $item) {
                $result[] = [
                    'id' => $item['id'],
                    'username' => $item['username'] ?? '', // 用户名
                    'mobile' => $item['mobile'] ?? '', // 手机号
                    'gender' => $item['gender'] ?? '-', // 性别
                    'avatar' => $item['avatar'] ?? '', // 头像
                    'create_time' => $item['create_time'] ? $item['create_time'] : '', // 创建时间
                    'amount' => $item['amount'] ?? '0.00', // 金额
                    'payment_status' => $item['payment_status'] ?? 0, // 支付状态
                    'pay_time' => $item['pay_time'] ? date('Y-m-d H:i:s', $item['pay_time']) : '', // 支付时间
                    'order_sn' => $item['order_sn'] ?? '', // 订单号
                    'course_name' => $item['course_name'] ?? '' // 课程名称
                ];
            }
    
            // 使用FastAdmin的标准返回格式
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'list' => $result,
                    'total' => count($result)
                ]
            ]);
        } catch (\Exception $e) {
            trace("查询学员列表错误: " . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => '查询学员列表失败']);
        }
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                ->where($where);
            $total = $total->where("status", "<>", -1);
            //if ($_SESSION['think']['admin']['group_id'] == 2) {
            //    $total = $total->where("city_id", "=", $_SESSION['think']['admin']['city_id']);
            //}
            $total = $total
                ->count();

            $list = $this->model
                ->where($where);
            //if ($_SESSION['think']['admin']['group_id'] == 2) {
            //    $list = $list->where("city_id", "=", $_SESSION['think']['admin']['city_id']);
            //}
            $list = $list->where("status", "<>", -1)
                ->order('weigh desc,id desc')
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();
            //$this->model->getLastSql();
            $list = collection($list)->toArray();

//            $tree = Tree::instance();
//            $tree->init($list, 'pid');
//            $list = $tree->getTreeList($tree->getTreeArray(0), 'name');
//            $data = array();
//            for ($i = 0; $i < count($list); $i++) {
//                $v = $list[$i];
//                $data[] = [
//                    'id' => $v['id'],
//                    'parent' => $v['pid'] ? $v['pid'] : '#',
//                    'name' => $v['name'],
//                    'type' => $v['type'],
//                    'status' => $v['status'],
//                    'school_id' => model("School")->getSchool_idTextAttr($v['school_id'])
//                ];
//            }


            for($i=0;$i<count($list);$i++){
//                $list[$i]['province_id']=model('Cityarea')->getProvince_idTextAttr($list[$i]['province_id']);
//                $list[$i]['city_id']=model('Cityarea')->getCity_idTextAttr($list[$i]['city_id']);
//                $list[$i]['district_id']=model('Cityarea')->getDistrict_idTextAttr($list[$i]['district_id']);
//                $list[$i]['province_id']=preg_replace("/\/$/","",model('Cityarea')->getProvince_idTextAttr($list[$i]['province_id'])."/".$list[$i]['city_id']."/".$list[$i]['district_id']);
//                $list[$i]['school_id']=model('School')->getSchool_idTextAttr($list[$i]['school_id'],$list[$i]);
                //$v['table_id']=$this->model->getTable_idTextAttr($v['table_id'],$v);
                $list[$i]['table_id']=$this->model->getTable_idTextAttr($list[$i]['table_id'],$list[$i]);
                //$v['is_real']=$this->model->getIs_realTextAttr($v['is_real'],$v);
                $list[$i]['is_real']=$this->model->getIs_realTextAttr($list[$i]['is_real'],$list[$i]);
                //$v['is_close']=$this->model->getIs_closeTextAttr($v['is_close'],$v);
                $list[$i]['is_close']=$this->model->getIs_closeTextAttr($list[$i]['is_close'],$list[$i]);
                //$v['is_opened']=$this->model->getIs_openedTextAttr($v['is_opened'],$v);
                $list[$i]['is_opened']=$this->model->getIs_openedTextAttr($list[$i]['is_opened'],$list[$i]);
                //$v['is_finished']=$this->model->getIs_finishedTextAttr($v['is_finished'],$v);
                $list[$i]['is_finished']=$this->model->getIs_finishedTextAttr($list[$i]['is_finished'],$list[$i]);
                //$v['is_user_end']=$this->model->getIs_user_endTextAttr($v['is_user_end'],$v);
                $list[$i]['is_user_end']=$this->model->getIs_user_endTextAttr($list[$i]['is_user_end'],$list[$i]);
                //$v['is_end']=$this->model->getIs_endTextAttr($v['is_end'],$v);
                $list[$i]['is_end']=$this->model->getIs_endTextAttr($list[$i]['is_end'],$list[$i]);
                $list[$i]['apply_type']=$this->model->getApply_typeTextAttr($list[$i]['apply_type'],$list[$i]);
            }
            
            
            $result = array("total" => $total, "rows" => $list);
            return json($result);
        }

        Db::execute("UPDATE eb_apply_form_item AS a
INNER JOIN fa_order AS o ON a.user_id = o.user_id AND a.classes_id = o.classes_id
SET a.order_id = o.id WHERE a.order_id IS NULL");
        
//        $this->assignconfig("relType",$_REQUEST['relType']);
//        $this->assignconfig("relId",$_REQUEST['relId']);
        $this->assignconfig("admin",$_SESSION['think']['admin']);
        return $this->view->fetch();
    }
    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        //$this->model->validate($validate);
                    }
                    $datatime=date("Y-m-d H:i:s");
                    $params['create_time']=$datatime;
                    unset($params['delete_time']);
                    unset($params['update_time']);
                    $result = $this->model->allowField(true)->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($this->model->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->assignconfig("admin",$_SESSION['think']['admin']);
        $start_time=date('Y-m-d H:i:s');
        $this->view->assign("start_time", $start_time);
        $end_time=date('Y-m-d H:i:s', strtotime("+2 month"));
        $this->view->assign("end_time", $end_time);
        $add_time=time();
        $this->view->assign("add_time", $add_time);
        $dead_time=date('Y-m-d H:i:s', strtotime("+1 year"));
        $this->view->assign("dead_time", $dead_time);
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = NULL)
    {
        $row = $this->model->get($ids);
        if (!$row)
            $this->error(__('No Results were found'));
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        //$row->validate($validate);
                    }
                    $datatime=date("Y-m-d H:i:s");
                    $params['update_time']=$datatime;
                    unset($params['delete_time']);
                    unset($params['create_time']);
//                    if($_SESSION['think']['admin']['group_id']==2){
//                        $params['city_id']=$_SESSION['think']['admin']['city_id'];
//                    }
                    $result = $row->allowField(true)->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($row->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->assignconfig("admin",$_SESSION['think']['admin']);
        $this->view->assign("row", $row);
        return $this->view->fetch();

    }

    /**
     * 添加
     */
    public function view($ids = NULL)
    {
        $row=$this->model->getRow($ids);
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    public function lists($ids = NULL){
        $where="where 1=1";
        if($_SESSION['think']['admin']['group_id']!=1){
            $where.=" and city_id='{$_SESSION['think']['admin']['city_id']}'";
        }
        $page=1;
        $pagesize=50;
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                $keyField = $this->request->request("keyField");
                $keyValue = $this->request->request("keyValue");
                $page = $this->request->request("pageNumber");
                $page = ($page == null) ? 1 : $page;
                $pagesize = $this->request->request("pageSize");
                $pagesize = ($pagesize == null) ? 10 : $pagesize;
                if ($keyValue != "") {
                    $nn=preg_split("/\,/",$keyValue);
                    for($i=0;$i<count($nn);$i++) {
                        $where.= " and $keyField='{$nn[$i]}'";
                    }
                }
            }else{
                $where="where 1=1";
                if(isset($_REQUEST['city_id'])&&$_REQUEST['city_id']>0){
                    //$where.=" and city_id=".$_REQUEST['city_id'];
                }
            }
        }
        $sql="select id,title name from dev002_gxyusheng.apply_form $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
        $list = db()->query($sql);

//        $r_name = model("School")->field("id,name")->where("id", "=", $_REQUEST['school_id'])->select();
//        if (empty($list)&&!empty($r_name)) {
//            $data = array(
//                "type" => "file",
//                "pid" => 0,
//                "name" => $r_name[0]['name']."学科",
//                "title" => $r_name[0]['name'],
//                "ismenu" => 0,
//                "createtime" => time(),
//                "updatetime" => time(),
//                "status" => 1,
//                "school_id" => $_REQUEST['school_id'],
//            );
//            $this->model->create($data);
//        }
//        $sql="select id,pid,name,title,status from dev002_gxyusheng.apply_form $where limit " . (($page - 1) * $pagesize) . ",$pagesize";
//        $list = db()->query($sql);
//        $tree = Tree::instance();
//        $tree->init($list, 'pid');
//        $list = $tree->getTreeList($tree->getTreeArray(0), 'name');

        $total = db()->query("select count(1) as c from  dev002_gxyusheng.apply_form $where")[0];
        echo json_encode(array("list" => $list, "total" => $total['c']));
        exit;
    }

    public function status($ids = '')
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }
        try {
            $result=$this->model->where("id","=",$ids)->field("status")->find();
            $v=$result->status==1?0:1;
            $this->model->where("id","=",$ids)->update(["status"=>$v]);
            $this->success('切换成功!');
        } catch (Exception $e) {
            $this->error('切换失败!'.$e->getMessage());
        }
    }

    public function changeStatus($ids = '')
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->error('没有选中!');
        }
        try {
            $result=$this->model->where("id","=",$ids)->field("status")->find();
            $v=$result->status==1?0:1;
            $this->model->where("id","=",$ids)->update(["status"=>$v]);
            $this->success('切换成功!');
        } catch (Exception $e) {
            $this->error('切换失败!'.$e->getMessage());
        }
    }

    //从classplant读出最新的coursetable中前3个项目中所有的subject_lv，并转化成中文名

}
